import useTicketDetails from 'pages/tickets/hooks/useTicketDetails.ts';
import { useParams } from 'react-router-dom';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import FileViewer from 'pages/tickets/components/FileViewer.tsx';
// import SupportChat from 'pages/tickets/components/SupportChat.tsx';

export default function TicketDetails() {
  const { id = '' } = useParams();
  const { details, detailsLoading } = useTicketDetails({ id });

  if (!details || detailsLoading) return <CircularProgress />;

  return (
    <Stack spacing={2} direction="column">
      <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
          <Typography variant="h5" fontWeight={600}>
            {details.ticketNumber}
          </Typography>
        </Stack>
      </Paper>

      <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
        <Stack
          direction={{ xs: 'column' }}
          gap={{
            xs: 0,
            sm: 2,
          }}
        >
          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Name:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.name}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Email:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.email}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Subject:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.subject}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Type:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.inquiryType}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Description:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.description}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Status:
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.status}
            </Typography>
          </Box>

          <Box width={{ xs: '100%' }} sx={{ display: 'flex', flexDirection: 'row', gap: 3 }}>
            <Typography variant="body2" fontWeight="bold" sx={{ width: '200px' }}>
              Created At
            </Typography>
            <Typography variant="body2" color="text.primary">
              {details.createdAt.split('T')[0]}
            </Typography>
          </Box>
        </Stack>
      </Paper>

      {/* <SupportChat /> */}

      <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
        <Typography variant="h5" fontWeight={600}>
          Attachments
        </Typography>
        <Box>
          {details.attachments.map((fileUrl) => (
            <FileViewer fileUrl={fileUrl} />
          ))}
        </Box>
      </Paper>
    </Stack>
  );
}