import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { AccordionDetails, InputLabel } from '@mui/material';
import Paper from '@mui/material/Paper';
import FileInputWithPreview from 'components/file-input.tsx';
import SelectMenu, { SelectedValue } from 'components/select-menu.tsx';
import Checkbox from '@mui/material/Checkbox';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccordionSummary from '@mui/material/AccordionSummary';
import Accordion from '@mui/material/Accordion';
import useTemplateForm from 'pages/templates/hooks/useTemplateForm.ts';
import useCategories from 'pages/category/hooks/useCategories.ts';
import { Editor } from '@tinymce/tinymce-react';
import { useRef } from 'react';
import { useParams } from 'react-router-dom';
import useTemplateDetails from 'pages/templates/hooks/useTemplateDetails.ts';
import { preOrderOptions, sellingOptions } from 'pages/templates/const/sellingOtions.ts';
import { regionsList } from 'pages/templates/const/regionList.ts';
import { languages } from 'pages/templates/const/languages.ts';
import { Editor as TinyMCEEditor } from 'tinymce';
import CircularProgress from '@mui/material/CircularProgress';
import { fontFamily } from 'theme/typography.ts';
import { editorProps } from 'constant/editor.ts';
import SingleFileInput from 'components/single-file-input.tsx';
import { productTypes } from 'pages/templates/const/productType.ts';

export default function TemplateEdit() {
  const editorRef = useRef<TinyMCEEditor | null>(null);

  const { id = '' } = useParams();

  const { templateDetails } = useTemplateDetails({ _id: id });
  const { categories } = useCategories({});

  const { formik } = useTemplateForm({ templateDetails });

  const handleSelectChange = (id: string, value: SelectedValue | undefined) => {
    formik.setFieldValue(id, value);
  };

  const handleCheckboxChange = (language: string, isChecked: boolean) => {
    if (isChecked) formik.setFieldValue('languages', [...formik.values.languages, language]);
    else
      formik.setFieldValue(
        'languages',
        formik.values.languages.filter((lang) => lang !== language),
      );
  };

  const getSubCategoryOptions = () => {
    const defaultOption = { _id: null, name: 'None' };
    if (!categories) return [defaultOption];

    const selectedCategory = categories.find(
      (category) => category._id === formik.values.category._id,
    );
    const childrenOptions =
      selectedCategory?.children.map((category) => ({
        _id: category._id,
        name: category.categoryName.en,
      })) ?? [];
    return [...childrenOptions, defaultOption];
  };

  if (!templateDetails || !categories) {
    return <CircularProgress />;
  }

  return (
    <Paper elevation={3} sx={{ py: 4, width: '75%' }}>
      <Stack direction="row" alignItems="center" gap={2}>
        <Typography variant="h5" fontWeight={600}>
          Edit Template
        </Typography>
      </Stack>
      <Stack onSubmit={formik.handleSubmit} component="form" direction="column" gap={2} mt={4}>
        <SingleFileInput
          label="Cover Image"
          getFile={(files) => formik.setFieldValue('coverImage', files)}
          existingFile={templateDetails.coverImage}
        />

        {formik.errors.coverImage && (
          <Typography
            variant="h6"
            color="red"
            fontSize={12}
            fontWeight={400}
            fontFamily={fontFamily.workSans}
          >
            {formik.errors.coverImage}
          </Typography>
        )}
        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="templateName"
          >
            Template Name
          </InputLabel>
          <TextField
            id="templateName"
            type="text"
            variant="filled"
            placeholder="Template Name"
            autoComplete="description"
            fullWidth
            required
            error={!!formik.errors.templateName && formik.touched.templateName}
            helperText={
              formik.errors.templateName && formik.touched.templateName
                ? formik.errors.templateName
                : ''
            }
            {...formik.getFieldProps('templateName')}
          />
        </Box>
        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="serviceFee"
          >
            Service Fee
          </InputLabel>
          <TextField
            id="serviceFee"
            type="text"
            variant="filled"
            placeholder="Service Fee"
            autoComplete="description"
            fullWidth
            required
            error={!!formik.errors.serviceFee && formik.touched.serviceFee}
            helperText={
              formik.errors.serviceFee && formik.touched.serviceFee ? formik.errors.serviceFee : ''
            }
            {...formik.getFieldProps('serviceFee')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="templateName"
          >
            Template Slug
          </InputLabel>
          <TextField
            id="slug"
            type="text"
            variant="filled"
            placeholder="Template Slug"
            autoComplete="description"
            fullWidth
            {...formik.getFieldProps('slug')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="listingType"
          >
            Listing Type
          </InputLabel>
          <SelectMenu
            id="listingType"
            value={formik.values.listingType}
            handleChange={handleSelectChange}
            options={productTypes}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="category"
          >
            Category
          </InputLabel>
          <SelectMenu
            id="category"
            value={
              formik.values.category._id
                ? categories
                    .filter((item) => item._id === formik.values.category._id)
                    .map((item) => ({ _id: item._id, name: item.categoryName.en }))[0] || {
                    _id: null,
                    name: 'None',
                  }
                : formik.values.category
            }
            handleChange={handleSelectChange}
            options={[
              { _id: null, name: 'None' },
              ...categories.map((item) => ({ _id: item._id, name: item.categoryName.en })),
            ]}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="subcategory"
          >
            Sub Category
          </InputLabel>
          <SelectMenu
            id="subcategory"
            value={
              formik.values.subcategory._id
                ? getSubCategoryOptions()
                    .filter((item) => item._id === formik.values.subcategory._id)
                    .map((item) => ({ _id: item._id, name: item.name }))[0] || {
                    _id: null,
                    name: 'None',
                  }
                : formik.values.subcategory
            }
            handleChange={handleSelectChange}
            options={getSubCategoryOptions()}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="region"
          >
            Region
          </InputLabel>
          <SelectMenu
            id="region"
            value={
              formik.values.region._id
                ? regionsList.find((item) => item._id === formik.values.region._id) ?? {
                    _id: null,
                    name: '--Choose--',
                  }
                : formik.values.listingType
            }
            handleChange={handleSelectChange}
            options={[{ _id: null, name: '--Choose--' }, ...regionsList]}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="genres"
          >
            Genres
          </InputLabel>
          <TextField
            id="genres"
            type="text"
            variant="filled"
            placeholder="Genres"
            autoComplete="genres"
            fullWidth
            {...formik.getFieldProps('genres')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="releaseDate"
          >
            Release Date
          </InputLabel>
          <TextField
            id="releaseDate"
            type="date"
            variant="filled"
            placeholder="Template Slug"
            autoComplete="description"
            fullWidth
            {...formik.getFieldProps('releaseDate')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="preOrder"
          >
            Pre-Order
          </InputLabel>
          <SelectMenu
            id="preOrder"
            value={
              preOrderOptions.find((item) => item._id === formik.values.preOrder._id) ?? {
                _id: null,
                name: '--Choose--',
              }
            }
            handleChange={handleSelectChange}
            options={[{ _id: null, name: '--Choose--' }, ...preOrderOptions]}
          />
        </Box>

        <Box>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Checkbox
              inputProps={{ 'aria-label': 'dlc' }}
              onChange={(e) => formik.setFieldValue('dlc', e.target.checked)}
              checked={formik.values.dlc}
            />
            <InputLabel
              component="label"
              sx={{ fontSize: '12px', marginBottom: '20px' }}
              size="small"
              htmlFor="dlc"
            >
              DLC
            </InputLabel>
          </Stack>
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="price"
          >
            Price ($)
          </InputLabel>
          <TextField
            id="price"
            type="number"
            variant="filled"
            placeholder="Template price"
            autoComplete="price"
            fullWidth
            {...formik.getFieldProps('price')}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="specificCountrySellingOption"
          >
            Specific Country Selling Option
          </InputLabel>
          <SelectMenu
            id="specificCountrySellingOption"
            value={
              formik.values.specificCountrySellingOption._id
                ? sellingOptions.find(
                    (item) => item._id === formik.values.specificCountrySellingOption._id,
                  ) ?? { _id: null, name: '--Choose--' }
                : formik.values.specificCountrySellingOption
            }
            handleChange={handleSelectChange}
            options={[{ _id: null, name: '--Choose--' }, ...sellingOptions]}
          />
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="Language"
          >
            Language
          </InputLabel>
          {languages.map((language) => (
            <Box display="flex" alignItems="center" gap="10px" key={language}>
              <Checkbox
                inputProps={{ 'aria-label': language }}
                checked={formik.values.languages.includes(language)}
                onChange={(e) => handleCheckboxChange(language, e.target.checked)}
              />
              <InputLabel
                component="label"
                sx={{ fontSize: '12px', marginBottom: '20px' }}
                size="small"
                htmlFor="language"
              >
                {language}
              </InputLabel>
            </Box>
          ))}
        </Box>

        <Box>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="videos"
          >
            Add Video
          </InputLabel>
          <TextField
            id="videos"
            type="text"
            variant="filled"
            placeholder="Template videos"
            autoComplete="description"
            fullWidth
            {...formik.getFieldProps('videos')}
            onChange={(e) => formik.setFieldValue('videos', e.target.value)}
          />
        </Box>

        <Box>
          <FileInputWithPreview
            label="Add Image"
            getFile={(files, filePreviews) =>
              formik.setFieldValue('images', [...files, ...filePreviews])
            }
            existingFile={templateDetails.images}
          />
        </Box>

        <Box>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography>Details</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box>
                <InputLabel
                  component="label"
                  sx={{ fontSize: '12px', marginBottom: '20px' }}
                  size="small"
                  htmlFor="details.title"
                >
                  Title
                </InputLabel>
                <TextField
                  id="details.title"
                  type="text"
                  variant="filled"
                  placeholder="Title"
                  autoComplete="details.title"
                  fullWidth
                  {...formik.getFieldProps('details.title')}
                />
              </Box>
              <Box>
                <InputLabel
                  component="label"
                  sx={{ fontSize: '12px', marginBottom: '20px' }}
                  size="small"
                  htmlFor="details.description"
                >
                  Description
                </InputLabel>
                <Editor
                  onInit={(_evt, editor) => (editorRef.current = editor)}
                  onBlur={() =>
                    formik.setFieldValue('details.description', editorRef.current?.getContent())
                  }
                  initialValue={formik.values.details.description}
                  {...editorProps}
                />
              </Box>
              <Stack direction="column" mt={4} gap={1}>
                <Typography variant="h5" fontWeight={600}>
                  SEO
                </Typography>
                <TextField
                  id="details.seo.metaTitle"
                  type="text"
                  variant="filled"
                  placeholder="Title"
                  autoComplete="details.seo.metaTitle"
                  fullWidth
                  {...formik.getFieldProps('details.seo.metaTitle')}
                />
                <TextField
                  id="details.seo.metaDescription"
                  type="text"
                  variant="filled"
                  placeholder="Description"
                  autoComplete="details.seo.metaDescription"
                  fullWidth
                  {...formik.getFieldProps('details.seo.metaDescription')}
                />
                <TextField
                  id="details.seo.metaKeywords"
                  type="text"
                  variant="filled"
                  placeholder="Keywords"
                  autoComplete="details.seo.metaKeywords"
                  fullWidth
                  {...formik.getFieldProps('details.seo.metaKeywords')}
                />
              </Stack>
            </AccordionDetails>
          </Accordion>
        </Box>

        <Button
          type="submit"
          variant="contained"
          size="medium"
          fullWidth
          disabled={!formik.isValid || formik.isSubmitting}
        >
          Submit
        </Button>
      </Stack>
    </Paper>
  );
}