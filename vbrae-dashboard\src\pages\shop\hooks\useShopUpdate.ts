import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { patchShop } from 'pages/shop/api/patchShop.ts';

export default function useShopUpdate(props: { _id: string; isApproved: string }) {
  const queryClient = useQueryClient();
  const { refetch: updateRefetch, loading: updateLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['update-shop', { ...props }],
      queryFn: () => patchShop(props),
      onError: showError,
      enabled: false,
      onSuccess: () => queryClient.invalidateQueries('all-shop-requests').finally(),
    }),
    transform: (data) => data,
  });
  return { updateRefetch, updateLoading };
}