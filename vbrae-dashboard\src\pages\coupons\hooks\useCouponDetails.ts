import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getCouponDetails } from 'pages/coupons/api/getCouponDetails.ts';

export default function useCouponDetails(props: { _id: string }) {
  const { data: couponDetails, loading } = useQueryFix({
    query: useQuery({
      queryKey: ['coupon-details', props._id],
      queryFn: () => getCouponDetails(props),
      onError: showError,
      refetchOnWindowFocus: false,
    }),
    transform: (response) => response.data,
  });
  return { couponDetails, loading };
}
