import React, { useState } from "react";
import { TextField, Chip, Box } from "@mui/material";
import Stack from "@mui/material/Stack";

type ChipsProps =  {
    chips: string[];
    setChips: (chips: string[]) => void;
}

const ChipInput = ({ chips, setChips }: ChipsProps) => {
  const [inputValue, setInputValue] = useState('');

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && inputValue.trim() !== '') {
      event.preventDefault();
      setChips([...chips, inputValue.trim()]);
      setInputValue('');
    }
  };

  const handleDeleteChip = (chipToDelete: string) => {
    setChips(chips.filter((chip) => chip !== chipToDelete));
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexWrap: 'wrap',
        flexDirection: 'column',
        padding: '4px',
        '&:focus-within': { borderColor: 'blue' },
      }}
    >
      <TextField
        variant="outlined"
        placeholder="Add tags..."
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        sx={{
          flex: 1,
          '& .MuiOutlinedInput-notchedOutline': { border: 'none' },
        }}
      />

      <Stack gap={2} mt={2}>
        {chips.map((chip, index) => (
          <Chip
            key={index}
            label={chip}
            onDelete={() => handleDeleteChip(chip)}
            sx={{
              fontSize: '12px',
              borderRadius: '10px',
              paddingLeft: '10px',
              '& .MuiChip-deleteIcon': {
                marginLeft: '8px', // Adjust spacing between text and delete icon
              },
            }}
          />
        ))}
      </Stack>
    </Box>
  );
};

export default ChipInput;
