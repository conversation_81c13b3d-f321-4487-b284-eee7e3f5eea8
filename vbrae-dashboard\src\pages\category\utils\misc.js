export default function generateAccordion(categories) {
    return categories.map(item => ({
        _id: item._id,
        title: item.categoryName.en,
        visibility: item.visibility,
        showInMainMenu: item.showInMainMenu,
        children: item.children.map(subCategory => ({
            title: subCategory.categoryName.en,
            _id: subCategory._id,
            visibility: subCategory.visibility,
            showInMainMenu: subCategory.showInMainMenu
        }))
    }));
}
