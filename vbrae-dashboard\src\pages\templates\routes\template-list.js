import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { TableComponent } from 'components/table/table.tsx';
import { useEffect, useMemo, useState } from 'react';
import { InputBase } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import Paper from '@mui/material/Paper';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import useAllTemplates from 'pages/templates/hooks/useAllTemplates.ts';
import Button from '@mui/material/Button';
import { useNavigate } from 'react-router-dom';
import Stack from '@mui/material/Stack';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import useRemoveTemplate from 'pages/templates/hooks/useRemoveTemplate.ts';
export default function TemplateList() {
    const { data, loading } = useAllTemplates();
    const navigate = useNavigate();
    const [templateId, setTemplateId] = useState('');
    const { templateDelete } = useRemoveTemplate({ _id: templateId });
    const [searchValue, setSearchValue] = useState('');
    const columns = useMemo(() => [
        {
            header: 'Sr. No',
            accessorFn: (_, index) => `${index + 1}`,
            id: '_index',
            cell: (info) => info.getValue(),
            enableGlobalFilter: true,
        },
        {
            header: 'Title',
            accessorFn: (row) => row.templateName,
            id: 'templateName',
            cell: (info) => info.getValue(),
            enableGlobalFilter: true,
        },
        {
            header: 'Genres',
            accessorFn: (row) => row.genres,
            id: 'genres',
            cell: (info) => info.getValue(),
            enableGlobalFilter: true,
        },
        {
            header: 'Status',
            accessorFn: (row) => row.active ? 'Active' : 'Disabled',
            id: 'active',
            cell: (info) => _jsx(Typography, { children: info.row.original.active ? 'Active' : 'Disabled' }),
            enableGlobalFilter: true,
        },
        {
            header: 'Action',
            accessorFn: () => { },
            id: 'action',
            cell: (info) => (_jsxs(Stack, { direction: "row", spacing: 1, alignItems: "center", children: [_jsx(Button, { sx: { padding: 0, minWidth: 0 }, children: _jsx(IconifyIcon, { icon: "ion:copy-outline", sx: { fontSize: '20px' } }) }), _jsx(Button, { onClick: () => navigate(`/templates/${info.row.original._id}`), sx: { padding: 0, minWidth: 0 }, children: _jsx(IconifyIcon, { icon: "ion:create-outline", sx: { fontSize: '20px' } }) }), _jsx(Button, { sx: { padding: 0, minWidth: 0 }, children: _jsx(IconifyIcon, { icon: "ion:trash-outline", sx: { fontSize: '20px' }, onClick: () => setTemplateId(info.row.original._id) }) })] })),
            enableGlobalFilter: false, // Actions don't need to be globally searchable
        },
    ], []);
    useEffect(() => {
        if (!templateId)
            return;
        templateDelete().finally();
    }, [templateId]);
    if (loading || !data)
        return _jsx("div", { children: "Loading..." });
    return (_jsxs(Stack, { direction: "column", spacing: 2, children: [_jsx(Typography, { variant: "h5", fontWeight: 600, letterSpacing: 1, fontFamily: fontFamily.workSans, display: { xs: 'none', lg: 'block' }, children: "Offer List" }), _jsxs(Paper, { component: "form", sx: {
                    p: '2px 4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 400,
                    marginLeft: 'auto',
                }, children: [_jsx(InputBase, { sx: { ml: 1, flex: 1, border: 'none' }, placeholder: "Search here", inputProps: { 'aria-label': 'search google maps' }, onChange: (e) => setSearchValue(e.target.value.trim()) }), _jsx(IconButton, { type: "button", sx: { p: '10px' }, "aria-label": "search", children: _jsx(SearchIcon, {}) })] }), _jsx(TableComponent, { columns: columns, data: data, globalFilter: searchValue, setGlobalFilter: setSearchValue })] }));
}
