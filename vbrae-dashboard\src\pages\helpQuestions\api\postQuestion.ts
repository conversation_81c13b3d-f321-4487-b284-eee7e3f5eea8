import {postRequest} from "vbrae-utils";

type Props = {
    title: string;
    content: string;
    isActive: boolean;
    categoryId: string;
    subcategoryId: string;
}

export async function postQuestion(props: Props): Promise<{message: string}> {
    const r = await postRequest<Props>({
        url: `help/${props.categoryId}/subcategories/${props.subcategoryId}/questions`,
        data: props,
        useAuth: true,
    });
    return r.response;
}