import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useRef } from 'react';
import { fontFamily } from 'theme/typography';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import IconifyIcon from 'components/base/IconifyIcon';
import VisitorsChartLegends from './VisitorsChartLegends';
import VisitorsChart from './VisitorsChart';
const WebsiteVisitors = () => {
    const chartRef = useRef(null);
    return (_jsxs(Paper, { sx: { height: 500 }, children: [_jsxs(Stack, { alignItems: "center", justifyContent: "space-between", mb: -2, children: [_jsx(Typography, { variant: "h6", fontWeight: 400, fontFamily: fontFamily.workSans, children: "Website Visitors" }), _jsx(But<PERSON>, { variant: "contained", color: "secondary", size: "medium", endIcon: _jsx(IconifyIcon, { icon: "mingcute:arrow-down-line" }), sx: { py: 0.875, zIndex: 1000 }, children: "Export" })] }), _jsx(VisitorsChart, { chartRef: chartRef }), _jsx(VisitorsChartLegends, { chartRef: chartRef })] }));
};
export default WebsiteVisitors;
