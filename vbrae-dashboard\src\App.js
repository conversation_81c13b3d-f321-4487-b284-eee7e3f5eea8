import { jsx as _jsx } from "react/jsx-runtime";
import { <PERSON>rowser<PERSON>outer } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "react-query";
import BasicRouter from "routes/router.tsx";
const App = () => {
    const queryClient = new QueryClient();
    return (_jsx(BrowserRouter, { children: _jsx(QueryClientProvider, { client: queryClient, children: _jsx(BasicRouter, {}) }) }));
};
export default App;
