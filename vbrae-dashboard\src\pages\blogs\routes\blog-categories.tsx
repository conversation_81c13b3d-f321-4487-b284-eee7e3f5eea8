import Stack from '@mui/material/Stack';
import Paper from '@mui/material/Paper';
import BlogCategoryForm from 'pages/blogs/components/blog-category-form.tsx';
import BlogCategoryTable from 'pages/blogs/components/blog-category-table.tsx';

export default function BlogCategories() {
  return (
    <Stack component="div" direction="row" gap={2} mt={4} alignItems="start">
      <Paper
        elevation={3}
        sx={{ py: 3, width: { xs: '100%', md: '40%' } }}
      >
        <BlogCategoryForm />
      </Paper>

      <Paper elevation={3} sx={{ py: 3, width: { xs: '100%', md: '60%' } }}>
        <BlogCategoryTable />
      </Paper>
    </Stack>
  );
}