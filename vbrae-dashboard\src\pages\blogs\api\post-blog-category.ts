import { postRequest } from 'vbrae-utils';

type BlogCategoryProps = {
  language: string;
  categoryName: string;
  description: string;
  keywords: string[];
  order: number;
};

export async function postBlogCategory(props: BlogCategoryProps): Promise<undefined> {
    const r = await postRequest<BlogCategoryProps>({
        url: 'blog-category',
        data : props,
        useAuth: true
    });
    return r.response
}