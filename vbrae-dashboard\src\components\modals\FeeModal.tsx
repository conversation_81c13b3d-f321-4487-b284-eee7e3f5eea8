import * as React from 'react';
import {useEffect, useState} from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { DialogActions, InputLabel } from '@mui/material';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import useUpdateProduct from 'pages/products/hooks/useUpdateProduct.ts';

interface Option {
  _id: string;
  value: boolean;
  price: string;
}

interface InputModalProps {
  open: Option;
  setOpen: (open: Option) => void;
}

export default function FeeModal({ open, setOpen } : InputModalProps) {

    const [price, setPrice] = useState(open.price);
    const {updateRefetch, updateLoading} = useUpdateProduct({_id: open._id, expectedPrice: price});

    useEffect(() => setPrice(open.price), [open.price]);

    const handleClose = () => setOpen({_id: "", value: false, price: '1'});

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        await updateRefetch();
        handleClose()
    }

    return (
      <React.Fragment>
        <Dialog
          open={open.value}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          fullWidth={true}
        >
          <Stack direction="row" spacing={2} justifyContent="space-between">
            <DialogTitle id="alert-dialog-title" sx={{ padding: '10px 0' }}>
              Added Product Fee
            </DialogTitle>
            <DialogActions>
              <IconifyIcon icon="mingcute:close-line" onClick={handleClose} />
            </DialogActions>
          </Stack>
          <form onSubmit={handleSubmit}>
            <DialogContent sx={{ padding: 0 }}>
              <Box>
                <InputLabel
                  component="label"
                  sx={{ fontSize: '12px', marginBottom: '20px' }}
                  size="small"
                  htmlFor="expectedPrice"
                >
                  Expected Fee
                </InputLabel>
                <TextField
                  id="expectedPrice"
                  type="number"
                  required
                  value={price}
                  onChange={(e) => setPrice(e.target.value)}
                  fullWidth
                  sx={{ marginBottom: 2 }}
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleClose} variant="outlined" type="button">
                Cancel
              </Button>
              <Button type="submit" variant="contained" disabled={updateLoading}>
                {updateLoading ? 'Saving...' : 'Save'}
              </Button>
            </DialogActions>
          </form>
        </Dialog>
      </React.Fragment>
    );
}