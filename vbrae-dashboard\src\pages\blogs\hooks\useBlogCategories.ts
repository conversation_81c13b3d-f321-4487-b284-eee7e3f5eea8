import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getBlogCategories } from 'pages/blogs/api/get-blog-categories.ts';

export interface BlogCategoryProps {
  language: string;
}

export default function useBlogCategories(props : BlogCategoryProps) {
    const { data: categories, loading: categoryLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['blog-categories', { ...props }],
            queryFn: () => getBlogCategories(props),
            onError: showError,
        }),
        transform: (data) => data.data,
    });
    return { categories, categoryLoading };
}
