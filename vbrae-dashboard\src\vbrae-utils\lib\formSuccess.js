import Swal from 'sweetalert2';
export const showSuccess = (message, handleSuccess) => {
    Swal.fire({
        icon: 'success',
        title: 'Success',
        text: message,
        background: '#171E2E',
        color: 'white',
        allowOutsideClick: false,
        confirmButtonColor: '#212121',
    }).then((result) => {
        if (result.isConfirmed && handleSuccess) {
            handleSuccess();
        }
    });
};
