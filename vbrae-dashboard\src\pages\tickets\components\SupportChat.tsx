import { useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Chip, Typography } from '@mui/material';
import { format } from 'date-fns';
import { Link } from 'react-router-dom';
import ReplyForm from 'pages/tickets/components/ReplyForm.tsx';
import {
  Chat<PERSON>ontainer,
  MessageBubble,
  MessageList,
  TicketHeader,
} from 'pages/tickets/const/chat.ts';
import { useConversationDetails } from 'pages/tickets/hooks/useConversationDetails.ts';
import CircularProgress from '@mui/material/CircularProgress';
import { useMessages } from 'pages/tickets/hooks/useMessages.ts';
import { useReadMessage } from 'pages/tickets/hooks/useReadMessages.ts';
import { useProfile } from 'hooks/useProfile.ts';
import Box from "@mui/material/Box";

const TicketChat = () => {
  const messageListRef = useRef<HTMLElement | null>(null);

  const { user } = useProfile();
  const { conversation } = useConversationDetails();
  const { messages } = useMessages();
  const { refetch } = useReadMessage();

  useEffect(() => {
    if (!messages || messages.length === 0) return;
    const lastMessage = messages[messages.length - 1];
    const lastMessageElement = document.getElementById(`msg_${lastMessage._id}`);
    lastMessageElement?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    if (!messages) return;
    refetch().finally();
  }, [messages]);

  if (!conversation || !messages || !user) return <CircularProgress />;

  const nameToShow = conversation.client._id === user._id ? 'seller' : 'client';

  return (
    <ChatContainer>
        <TicketHeader>
            <Badge
                overlap="circular"
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                variant="dot"
                color="success"
            >
                <Avatar alt={conversation[nameToShow].name} src={conversation[nameToShow].avatar} />
            </Badge>

            <Box ml={2}>
                <Typography fontWeight={600} fontSize="14px">
                    {conversation[nameToShow].name}
                </Typography>
                <Typography fontSize="12px" color="text.secondary">
                    {conversation[nameToShow].role}
                </Typography>
                <Typography fontSize="12px" color="text.secondary">
                    {conversation[nameToShow].email}
                </Typography>
                <Typography fontSize="12px" color="text.secondary">
                    {conversation[nameToShow].country || 'Country Unknown'}
                </Typography>
            </Box>

            <Chip
                label="Active"
                sx={{
                    marginLeft: 'auto',
                    borderRadius: '16px',
                    px: 2,
                    py: 0.5,
                    backgroundColor: '#14CA74',
                    color: 'black',
                    fontWeight: 500,
                    fontSize: {
                        xs: '12px',
                        sm: '13px',
                    },
                    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
                }}
            />
        </TicketHeader>


        <MessageList ref={messageListRef}>
            {messages.map((message) => {
                const isSelf = message.sender._id === user._id;

                return (
                    <MessageBubble
                        key={message._id}
                        sx={{
                            alignSelf: isSelf ? 'flex-end' : 'flex-start',
                            backgroundColor: isSelf ? '#1E88E5' : '#F1F1F1',
                            color: isSelf ? '#fff' : '#000',
                            borderRadius: '16px',
                            padding: '10px 16px',
                            maxWidth: '80%',
                            marginBottom: '8px',
                        }}
                    >
                        <Typography variant="body1">{message.content}</Typography>

                        {/* Render attachments if present */}
                        {message.attachments.length > 0 && (
                            <div style={{ marginTop: '8px', display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                                {message.attachments.map((attachment, index) => (
                                    <Link to={attachment} key={index} target="_blank">
                                        <Chip
                                            label={`Attachment#${index + 1}`}
                                            variant="outlined"
                                            color="primary"
                                            sx={{
                                                margin: '2px',
                                                borderRadius: '16px',
                                                padding: '6px 12px',
                                                color: '#333',
                                                fontSize: {
                                                    xs: '12px',
                                                    sm: '14px',
                                                },
                                                '& .MuiChip-deleteIcon': {
                                                    marginLeft: '8px',
                                                    color: '#777',
                                                },
                                            }}
                                        />
                                    </Link>
                                ))}
                            </div>
                        )}

                        <Typography
                            variant="caption"
                            color={isSelf ? 'rgba(255,255,255,0.8)' : 'textSecondary'}
                            sx={{ display: 'block', marginTop: '4px', textAlign: isSelf ? 'right' : 'left' }}
                        >
                            {format(message.createdAt, 'HH:mm')}
                        </Typography>
                    </MessageBubble>
                );
            })}
        </MessageList>

      <ReplyForm />
    </ChatContainer>
  );
};

export default TicketChat;
