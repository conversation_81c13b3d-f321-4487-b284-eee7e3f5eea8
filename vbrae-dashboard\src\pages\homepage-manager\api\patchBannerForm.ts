import { patchRequest } from 'vbrae-utils';

interface BannerPropsDto {
      link: string;
      order: number;
      location: string;
      logoImage?: string;
    images: {
        desktop?: string,
        phone?: string,
        tablet?: string
    }
    _id: string;
}

export async function patchBannerForm(props: BannerPropsDto): Promise<{message: string} | undefined> {
    const r = await patchRequest({
        url: `banners/${props._id}`,
        data : props,
        useAuth: true
    });
    return r.response
}