import useBlogs from 'pages/blogs/hooks/useBlogs.ts';
import { useNavigate } from 'react-router-dom';
import { useEffect, useMemo, useState } from 'react';
import SelectMenu, { SelectedValue } from 'components/select-menu.tsx';
import { languages } from 'pages/category/const/languages.ts';
import { ColumnDef } from '@tanstack/react-table';
import BasicDropdown from 'components/dropdown.tsx';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { fontFamily } from 'theme/typography.ts';
import Box from '@mui/material/Box';
import {AvatarGroup, InputBase, InputLabel} from '@mui/material';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import { TableComponent } from 'components/table/table.tsx';
import SearchIcon from '@mui/icons-material/Search';
import { BlogPost } from 'pages/blogs/types/blog.ts';
import useDeleteBlog from 'pages/blogs/hooks/useDeleteBlog.ts';
import Avatar from "@mui/material/Avatar";

export default function AllBlogs() {
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState('');
  const [blogToDelete, setBlogToDelete] = useState({ _id: '' });
  const [selectedLanguage, setSelectedLanguage] = useState<SelectedValue>(languages[0]);
  const { blogs, blogsLoading } = useBlogs({ language: selectedLanguage._id as string });
  const { blogDelete } = useDeleteBlog(blogToDelete);

  useEffect(() => {
    if (!blogToDelete._id) return;

    blogDelete().finally(() => setBlogToDelete({ _id: '' }));
  }, [blogToDelete]);

  const columns = useMemo<ColumnDef<BlogPost>[]>(
    () => [
        {
            header: 'Image',
            accessorFn: (row) => row.image,
            id: 'image',
            cell: (info) => <AvatarGroup spacing={24} sx={{justifyContent: "flex-end"}}>
                <Avatar alt="Remy Sharp" src={info.row.original.image} />
            </AvatarGroup>,
            enableGlobalFilter: true,
        },
      {
        header: 'Title',
        accessorFn: (row) => row.title,
        id: 'title',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Language',
        accessorFn: (row) => row.language,
        id: 'language',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Category',
        accessorFn: (row) => row.category.categoryName,
        id: 'categoryName',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Date',
        accessorFn: (row) => row.createdAt,
        id: 'createdAt',
        cell: (info) => <span>{info.row.original.createdAt.split('T')[0]}</span>,
        enableGlobalFilter: true,
      },
      {
        header: 'Actions',
        accessorFn: () => {},
        id: 'actions',
        cell: (info) => {
          const options = [
            {
              title: 'Edit',
              action: () => navigate(`/blogs/edit/${info.row.original._id}`),
            },
            {
              title: 'Delete',
              action: () => setBlogToDelete({ _id: info.row.original._id }),
            },
          ];
          return <BasicDropdown options={options} />;
        },
        enableGlobalFilter: false,
      },
    ],
    [],
  );

  if (blogsLoading || !blogs) return <CircularProgress />;

  return (
    <>
      <Stack direction="column" spacing={2}>
        <Typography
          variant="h5"
          fontWeight={600}
          letterSpacing={1}
          fontFamily={fontFamily.workSans}
          display={{ xs: 'none', lg: 'block' }}
        >
          Blog Categories
        </Typography>
      </Stack>

      <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
        <Box width={{ xs: '100%', sm: '200px' }}>
          <InputLabel
            component="label"
            sx={{ fontSize: '12px', marginBottom: '20px' }}
            size="small"
            htmlFor="language"
          >
            Language
          </InputLabel>
          <SelectMenu
            id="language"
            value={selectedLanguage}
            handleChange={(_, value) => setSelectedLanguage(value!)}
            options={languages}
          />
        </Box>
        <Box>
          <Paper
            component="form"
            sx={{
              p: '2px 4px',
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'center',
              width: { xs: '100%', sm: 300 },
            }}
          >
            <InputBase
              sx={{ ml: 1, flex: 1, border: 'none' }}
              placeholder="Search here"
              inputProps={{ 'aria-label': 'search' }}
              onChange={(e) => setSearchValue(e.target.value.trim())}
            />
            <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
              <SearchIcon />
            </IconButton>
          </Paper>
        </Box>
      </Stack>

      <Box sx={{ overflowX: 'auto', width: '100%' }}>
        <TableComponent
          columns={columns}
          data={blogs}
          globalFilter={searchValue}
          setGlobalFilter={setSearchValue}
        />
      </Box>
    </>
  );
}