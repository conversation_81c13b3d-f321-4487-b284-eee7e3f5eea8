import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import {getAllOrders} from "pages/orders/api/getAllOrders.ts";

export default function useAllOrders({ query }: { query: string }) {
  const {
    data: orders,
    loading: orderLoading,
    refetch: orderRefetch,
  } = useQueryFix({
    query: useQuery({
      queryKey: ['all-orders', query],
      queryFn: () => getAllOrders({ query }),
      onError: showError,
    }),
    transform: (data) => data.data.orders,
  });
  return { orders, orderLoading, orderRefetch };
}
