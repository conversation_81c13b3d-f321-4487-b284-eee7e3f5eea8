import Swal from 'sweetalert2';

type HandleSuccess = () => void;

export const showSuccess = (message: string, handleSuccess?: HandleSuccess): void => {
  Swal.fire({
    icon: 'success',
    title: 'Success',
    text: message,
    background: '#171E2E',
    color: 'white',
    allowOutsideClick: false,
    confirmButtonColor: '#212121',
  }).then((result: { isConfirmed: boolean }) => {
    if (result.isConfirmed && handleSuccess) {
      handleSuccess();
    }
  });
};
