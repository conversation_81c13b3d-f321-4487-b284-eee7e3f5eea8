import Swal from "sweetalert2";
export const showConfirmation = ({ handleDelete, id }) => {
    Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        showCancelButton: true,
        background: '#171E2E',
        confirmButtonColor: '#2DC071',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, delete it!',
    }).then((result) => {
        if (result.isConfirmed) {
            handleDelete(id);
        }
    });
};
