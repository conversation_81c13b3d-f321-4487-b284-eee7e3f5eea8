import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { FormControl, InputLabel, Radio, RadioGroup } from '@mui/material';
import Paper from '@mui/material/Paper';
import useCategoryForm from 'pages/category/hooks/useCategoryForm.ts';
import useCategoryDetails from 'pages/category/hooks/useCategoryDetails.ts';
import CircularProgress from '@mui/material/CircularProgress';
import SelectMenu from "components/select-menu.tsx";
import FormControlLabel from "@mui/material/FormControlLabel";
import FileInputWithPreview from "components/file-input.tsx";
import useParentCategory from "pages/category/hooks/useCategories.ts";
export default function CategoryEdit() {
    const { categories } = useParentCategory();
    const { categoryDetails, categoryDetailsLoading } = useCategoryDetails();
    const { formik } = useCategoryForm({ categoryDetails });
    if (categoryDetailsLoading || !categoryDetails) {
        return _jsx(CircularProgress, {});
    }
    const handleChange = (event, id) => {
        formik.setFieldValue(id, event.target.value);
    };
    const handleSelectChange = (id, value) => {
        formik.setFieldValue(id, value);
    };
    return (_jsxs(Paper, { elevation: 3, sx: { py: 4, width: '75%' }, children: [_jsxs(Stack, { direction: "row", alignItems: "center", justifyContent: "space-between", gap: 2, children: [_jsx(Typography, { variant: "h5", fontWeight: 600, children: "Edit Category" }), _jsx(Button, { type: "submit", variant: "contained", size: "small", children: "View Categories" })] }), _jsxs(Stack, { onSubmit: formik.handleSubmit, component: "form", direction: "column", gap: 2, mt: 4, children: [_jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "name", children: "Category Name (English)" }), _jsx(TextField, { id: "name", type: "text", variant: "filled", placeholder: "Category Name", autoComplete: "name", fullWidth: true, autoFocus: true, required: true, error: !!formik.errors.categoryName?.en && formik.touched.categoryName?.en, helperText: formik.errors.categoryName?.en && formik.touched.categoryName?.en ? formik.errors.categoryName?.en : '', ...formik.getFieldProps('categoryName.en') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "categoryName.de", children: "Category Name (German)" }), _jsx(TextField, { id: "categoryName.de", type: "text", variant: "filled", placeholder: "Category Name", autoComplete: "categoryName.de", fullWidth: true, required: true, ...formik.getFieldProps('categoryName.en') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "categoryName.de", children: "Category Name (German)" }), _jsx(TextField, { id: "categoryName.de", type: "text", variant: "filled", placeholder: "Category Name", autoComplete: "categoryName.de", fullWidth: true, required: true, ...formik.getFieldProps('categoryName.de') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "categoryName.fr", children: "Category Name (French)" }), _jsx(TextField, { id: "categoryName.fr", type: "text", variant: "filled", placeholder: "Category Name", autoComplete: "categoryName.fr", fullWidth: true, required: true, ...formik.getFieldProps('categoryName.fr') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "categoryName.it", children: "Category Name (Italian)" }), _jsx(TextField, { id: "categoryName.it", type: "text", variant: "filled", placeholder: "Category Name", autoComplete: "categoryName.it", fullWidth: true, required: true, ...formik.getFieldProps('categoryName.it') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "categoryName.es", children: "Category Name (Spanish)" }), _jsx(TextField, { id: "categoryName.es", type: "text", variant: "filled", placeholder: "Category Name", autoComplete: "categoryName.es", fullWidth: true, required: true, ...formik.getFieldProps('categoryName.es') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "metaTitle", children: "Title (Meta Tag)" }), _jsx(TextField, { id: "metaTitle", type: "text", variant: "filled", placeholder: "Category Title", autoComplete: "metaTitle", fullWidth: true, required: true, error: !!formik.errors.metaTitle && formik.touched.metaTitle, helperText: formik.errors.metaTitle && formik.touched.metaTitle ? formik.errors.metaTitle : '', ...formik.getFieldProps('metaTitle') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "metaDescription", children: "Description (Meta Tag)" }), _jsx(TextField, { id: "metaDescription", type: "text", variant: "filled", placeholder: "Category Description", autoComplete: "description", fullWidth: true, required: true, error: !!formik.errors.metaDescription && formik.touched.metaDescription, helperText: formik.errors.metaDescription && formik.touched.metaDescription
                                    ? formik.errors.metaDescription
                                    : '', ...formik.getFieldProps('metaDescription') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "order", children: "Order" }), _jsx(TextField, { id: "category_order", type: "number", variant: "filled", placeholder: "Category Order", autoComplete: "category_order", fullWidth: true, required: true, error: !!formik.errors.category_order && formik.touched.category_order, helperText: formik.errors.category_order && formik.touched.category_order ? formik.errors.category_order : '', ...formik.getFieldProps('category_order') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "parent_category", children: "Parent Category" }), categories && (_jsx(SelectMenu, { id: "category", value: formik.values.category._id
                                    ? categories
                                        .filter((item) => item._id === formik.values.category._id)
                                        .map((item) => ({ _id: item._id, name: item.categoryName.en }))[0] || { _id: '', name: '' }
                                    : formik.values.category, handleChange: handleSelectChange, options: [{ _id: null, name: "None" }, ...categories.map(item => ({ _id: item._id, name: item.categoryName.en }))] }))] }), _jsxs(Stack, { direction: "row", alignItems: "center", children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px', width: '50%' }, size: "small", htmlFor: "Visibility", children: "Visibility" }), _jsx(FormControl, { children: _jsxs(RadioGroup, { row: true, "aria-labelledby": "demo-row-radio-buttons-group-label", ...formik.getFieldProps('visibility'), onChange: (e) => handleChange(e, 'visibility'), children: [_jsx(FormControlLabel, { value: true, control: _jsx(Radio, {}), label: "Show" }), _jsx(FormControlLabel, { value: false, control: _jsx(Radio, {}), label: "Hide" })] }) })] }), _jsxs(Stack, { direction: "row", alignItems: "center", children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px', width: '50%' }, size: "small", htmlFor: "showInMainMenu", children: "Show on Main Menu" }), _jsx(FormControl, { children: _jsxs(RadioGroup, { row: true, "aria-labelledby": "demo-row-radio-buttons-group-label", ...formik.getFieldProps('showInMainMenu'), onChange: (e) => handleChange(e, 'showInMainMenu'), children: [_jsx(FormControlLabel, { value: true, control: _jsx(Radio, {}), label: "Yes" }), _jsx(FormControlLabel, { value: false, control: _jsx(Radio, {}), label: "No" })] }) })] }), _jsxs(Stack, { direction: "row", alignItems: "center", children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px', width: '50%' }, size: "small", htmlFor: "show-image-on-main-menu", children: "Show on Main Menu" }), _jsx(FormControl, { children: _jsxs(RadioGroup, { row: true, "aria-labelledby": "demo-row-radio-buttons-group-label", ...formik.getFieldProps('showImageOnMainMenu'), onChange: (e) => handleChange(e, 'showImageOnMainMenu'), children: [_jsx(FormControlLabel, { value: true, control: _jsx(Radio, {}), label: "Yes" }), _jsx(FormControlLabel, { value: false, control: _jsx(Radio, {}), label: "No" })] }) })] }), _jsx(FileInputWithPreview, { label: "Upload image", getFile: (file) => formik.setFieldValue('image', file[0]), existingFile: [categoryDetails.image] }), _jsx(Button, { type: "submit", variant: "contained", size: "medium", fullWidth: true, disabled: !formik.isValid || formik.isSubmitting, children: "Submit" })] })] }));
}
