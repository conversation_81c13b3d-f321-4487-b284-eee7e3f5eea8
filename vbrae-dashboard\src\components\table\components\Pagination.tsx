import { DOTS, usePagination } from '../hooks/usePagination';
import { IconButton, Tooltip, Box } from '@mui/material';
import { ChevronLeft, ChevronRight } from '@mui/icons-material';

type Props = {
  onPageChange: (selectedItem: number) => void;
  totalCount: number;
  siblingCount?: number;
  currentPage: number;
  pageSize: number;
  onNextButtonClick: () => void;
  onPreviousButtonClick: () => void;
};

const Pagination = (props: Props) => {
  const {
    onPageChange,
    totalCount,
    siblingCount = 1,
    currentPage,
    pageSize,
    onNextButtonClick,
    onPreviousButtonClick,
  } = props;

  const paginationRange = usePagination({
    currentPage,
    totalCount,
    siblingCount,
    pageSize,
  });

  if (currentPage === 0 || (paginationRange && paginationRange.length < 2)) {
    return null;
  }

  const onNext = () => {
    onNextButtonClick();
  };

  const onPrevious = () => {
    onPreviousButtonClick();
  };

  const lastPage = paginationRange && paginationRange[paginationRange.length - 1];

  return (
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2 }}>
        {/* Previous Button */}
        <Tooltip title="Previous" arrow>
        <span>
          <IconButton
              onClick={onPrevious}
              disabled={currentPage === 1}
              sx={{
                backgroundColor: 'transparent',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                },
              }}
          >
            <ChevronLeft />
          </IconButton>
        </span>
        </Tooltip>

        {/* Pagination Numbers */}
        {paginationRange &&
            paginationRange.map((pageNumber: any) => {
              if (String(pageNumber) === DOTS) {
                return (
                    <Box
                        key={pageNumber}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 32,
                          height: 32,
                          backgroundColor: 'transparent',
                          color: 'gray',
                          fontSize: '1rem',
                        }}
                    >
                      &#8230;
                    </Box>
                );
              }

              return (
                  <IconButton
                      key={pageNumber}
                      onClick={() => onPageChange(pageNumber - 1)}
                      sx={{
                        width: 32,
                        height: 32,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        fontSize: '1rem',
                        backgroundColor: currentPage === pageNumber ? 'rgba(0, 0, 0, 0.1)' : 'transparent',
                        '&:hover': {
                          backgroundColor: currentPage === pageNumber ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                        },
                      }}
                  >
                    {pageNumber}
                  </IconButton>
              );
            })}

        {/* Next Button */}
        <Tooltip title="Next" arrow>
        <span>
          <IconButton
              onClick={onNext}
              disabled={currentPage === lastPage}
              sx={{
                backgroundColor: 'transparent',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                },
              }}
          >
            <ChevronRight />
          </IconButton>
        </span>
        </Tooltip>
      </Box>
  );
};

export default Pagination;
