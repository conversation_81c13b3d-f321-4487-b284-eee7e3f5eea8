import * as Yup from 'yup';
import { useMutation } from 'react-query';
import { useFormik } from 'formik';
import { useParams, useSearchParams } from 'react-router-dom';
import { showError } from 'vbrae-utils';
import { uploadUrl } from 'pages/category/api/uploadUrl';
import { postMessage } from 'pages/tickets/api/postMessage.ts';
import { ErrorType } from 'pages/faq/hooks/useFaqForm.ts';

const schema = Yup.object().shape({
  content: Yup.string().required('Body is required'),
  attachments: Yup.array().of(Yup.mixed()).nullable(),
});

export default function useMessageForm() {

  const {id = ''} = useParams();
  const [searchParams] = useSearchParams();
  const conversationId = searchParams.get("conversationId") ?? "";

  const initialValues = {
    content: '',
    attachments: []
  }

  const { mutateAsync } = useMutation(postMessage, {
    onError: (error:ErrorType)=> showError(error),
  });

  const uploadImage = async (image:unknown): Promise<string> => {
    if (image instanceof File) {
      const fileType = image.name.split('.').pop()?.toLowerCase() || 'unknown';
      const resignedResponse = await uploadUrl({ name: image.name, fileType });
      const { url: resignedUrl, path: filePath } = resignedResponse;

      await fetch(resignedUrl, {
        method: 'PUT',
        headers: { 'Content-Type': image.type, 'x-amz-acl': 'public-read' },
        body: image,
      });

      return filePath;
    }
    return image as string;
  };

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: schema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      setSubmitting(true);
      const imageUrls: string[] = await Promise.all(values.attachments.map(async (attachment) => await uploadImage(attachment)))
      const response = await mutateAsync({ ...values, conversationId, attachments: imageUrls, ticketId: id });
      setSubmitting(false);
      resetForm();
      if (response) {
        return
      }
    },
  });

  return { formik };
}
