import {uploadUrl} from "pages/category/api/uploadUrl.ts";

export const editorProps = {
    apiKey: 'kib4z0a5ef24utq20ohc2sqvodd58y3u1mdswdety295un2h',
    init: {
        height: 500,
        menubar: false,
        plugins: [
            'advlist',
            'autolink',
            'lists',
            'link',
            'image',
            'charmap',
            'preview',
            'anchor',
            'searchreplace',
            'visualblocks',
            'code',
            'fullscreen',
            'insertdatetime',
            'media',
            'table',
            'code',
            'help',
            'wordcount',
        ],
        toolbar:
            'undo redo | blocks | ' +
            'bold italic forecolor | alignleft aligncenter ' +
            'alignright alignjustify | bullist numlist outdent indent | ' +
            'removeformat | image | help',
        content_style: `
          body {
            font-family: Helvetica, Arial, sans-serif;
            font-size: 14px;
            background-color: transparent;
          }
        `,
        images_upload_handler: async (blobInfo:any) => {
            try {
                const fileName = blobInfo.filename();
                const fileType = blobInfo.blob().type;

                const resignedResponse = await uploadUrl({ name: fileName, fileType });
                const { url: resignedUrl, path: filePath } = resignedResponse;

                await fetch(resignedUrl, {
                    method: "PUT",
                    headers: { "Content-Type": fileType, "x-amz-acl": "public-read" },
                    body: blobInfo.blob(),
                });

                return filePath;
            } catch (error) {
                console.error("Image upload failed:", error);
                throw new Error("Image upload failed");
            }
        }
    }
};
