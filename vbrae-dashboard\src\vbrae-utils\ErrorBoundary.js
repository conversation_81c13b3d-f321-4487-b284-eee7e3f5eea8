import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import React from 'react';
import { Button, Box, Typography, Container } from '@mui/material';
class ErrorBoundary extends React.Component {
    constructor() {
        super(...arguments);
        Object.defineProperty(this, "state", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: { hasError: false }
        });
    }
    static getDerivedStateFromError(error) {
        console.error(error);
        return { hasError: true };
    }
    componentDidCatch(error, errorInfo) {
        console.error(error, errorInfo); // Log error details
    }
    render() {
        if (this.state.hasError) {
            return (_jsx(Container, { maxWidth: "sm", children: _jsxs(Box, { sx: {
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: '100vh',
                        textAlign: 'center',
                        backgroundColor: 'rgba(0, 0, 0, 0.1)',
                        borderRadius: 2,
                        boxShadow: 3,
                        padding: 3,
                    }, children: [_jsx(Typography, { variant: "h4", component: "div", color: "error", sx: { fontWeight: 'bold', marginBottom: 2 }, children: "Something went wrong!" }), _jsxs(Typography, { variant: "body1", sx: { marginBottom: 3 }, children: ["Oops! There was an error.", _jsx("br", {}), "Our team has been notified, and we are working on a resolution for you!"] }), _jsx(Button, { variant: "contained", color: "primary", onClick: () => {
                                this.setState({ hasError: false });
                                window.location.href = '/';
                            }, children: "Back to Home" })] }) }));
        }
        else {
            return this.props.children;
        }
    }
}
export default ErrorBoundary;
