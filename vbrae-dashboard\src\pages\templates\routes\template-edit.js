import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Box from '@mui/material/Box';
import { AccordionDetails, InputLabel } from '@mui/material';
import Paper from '@mui/material/Paper';
import FileInputWithPreview from 'components/file-input.tsx';
import SelectMenu from 'components/select-menu.tsx';
import Checkbox from '@mui/material/Checkbox';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AccordionSummary from '@mui/material/AccordionSummary';
import Accordion from '@mui/material/Accordion';
import useTemplateForm from 'pages/templates/hooks/useTemplateForm.ts';
import useParentCategory from 'pages/category/hooks/useCategories.ts';
import { Editor } from '@tinymce/tinymce-react';
import { useRef } from 'react';
import { useParams } from 'react-router-dom';
import useTemplateDetails from 'pages/templates/hooks/useTemplateDetails.ts';
import { listingOptions, preOrderOptions, sellingOptions, } from 'pages/templates/const/sellingOtions.ts';
import { regionsList } from 'pages/templates/const/regionList.ts';
import { languages } from 'pages/templates/const/languages.ts';
export default function TemplateEdit() {
    const editorRef = useRef(null);
    const { id = '' } = useParams();
    const { templateDetails } = useTemplateDetails({ _id: id });
    const { categories } = useParentCategory();
    const { formik } = useTemplateForm({ templateDetails });
    const handleSelectChange = (id, value) => {
        formik.setFieldValue(id, value);
    };
    const handleCheckboxChange = (language, isChecked) => {
        if (isChecked)
            formik.setFieldValue("languages", [...formik.values.languages, language]);
        else
            formik.setFieldValue("languages", formik.values.languages.filter((lang) => lang !== language));
    };
    if (!templateDetails || !categories) {
        return _jsx("p", { children: "Loading..." });
    }
    console.log(formik.errors);
    return (_jsxs(Paper, { elevation: 3, sx: { py: 4, width: '75%' }, children: [_jsx(Stack, { direction: "row", alignItems: "center", gap: 2, children: _jsx(Typography, { variant: "h5", fontWeight: 600, children: "Edit Template" }) }), _jsxs(Stack, { onSubmit: formik.handleSubmit, component: "form", direction: "column", gap: 2, mt: 4, children: [_jsx(FileInputWithPreview, { label: 'Cover Image', getFile: (file) => formik.setFieldValue('image', file), existingFile: templateDetails.coverImage }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "templateName", children: "Template Name" }), _jsx(TextField, { id: "templateName", type: "text", variant: "filled", placeholder: "Template Name", autoComplete: "description", fullWidth: true, required: true, error: !!formik.errors.templateName && formik.touched.templateName, helperText: formik.errors.templateName && formik.touched.templateName
                                    ? formik.errors.templateName
                                    : '', ...formik.getFieldProps('templateName') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "templateName", children: "Template Slug" }), _jsx(TextField, { id: "slug", type: "text", variant: "filled", placeholder: "Template Slug", autoComplete: "description", fullWidth: true, required: true, error: !!formik.errors.slug && formik.touched.slug, helperText: formik.errors.slug && formik.touched.slug
                                    ? formik.errors.slug
                                    : '', ...formik.getFieldProps('slug') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "listingType", children: "Listing Type" }), _jsx(SelectMenu, { id: "listingType", value: formik.values.listingType._id
                                    ? listingOptions.find((item) => item._id === formik.values.listingType._id) ?? { _id: null, name: "--Choose--" }
                                    : formik.values.listingType, handleChange: handleSelectChange, options: [{ _id: null, name: "--Choose--" }, ...listingOptions] })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "category", children: "Category" }), _jsx(SelectMenu, { id: "category", value: formik.values.category._id
                                    ? categories
                                        .filter((item) => item._id === formik.values.category._id)
                                        .map((item) => ({ _id: item._id, name: item.categoryName.en }))[0] || { _id: null, name: 'None' }
                                    : formik.values.category, handleChange: handleSelectChange, options: [
                                    { _id: null, name: 'None' },
                                    ...categories.map((item) => ({ _id: item._id, name: item.categoryName.en })),
                                ] })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "region", children: "Region" }), _jsx(SelectMenu, { id: "region", value: formik.values.region._id
                                    ? regionsList.find((item) => item._id === formik.values.region._id) ?? { _id: null, name: "--Choose--" }
                                    : formik.values.listingType, handleChange: handleSelectChange, options: [{ _id: null, name: "--Choose--" }, ...regionsList] })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "genres", children: "Genres" }), _jsx(TextField, { id: "genres", type: "text", variant: "filled", placeholder: "Genres", autoComplete: "genres", fullWidth: true, required: true, error: !!formik.errors.genres && formik.touched.genres, helperText: formik.errors.genres && formik.touched.genres ? formik.errors.genres : '', ...formik.getFieldProps('genres') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "releaseDate", children: "Release Date" }), _jsx(TextField, { id: "releaseDate", type: "date", variant: "filled", placeholder: "Template Slug", autoComplete: "description", fullWidth: true, required: true, error: !!formik.errors.releaseDate && formik.touched.releaseDate, helperText: formik.errors.releaseDate && formik.touched.releaseDate
                                    ? formik.errors.releaseDate
                                    : '', ...formik.getFieldProps('releaseDate') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "preOrder", children: "Pre-Order" }), _jsx(SelectMenu, { id: "preOrder", value: preOrderOptions.find((item) => item._id === formik.values.preOrder._id) ?? { _id: null, name: "--Choose--" }, handleChange: handleSelectChange, options: [{ _id: null, name: "--Choose--" }, ...preOrderOptions] })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "templateName", children: "DLC" }), _jsx(TextField, { id: "dlc", type: "text", variant: "filled", placeholder: "Template dlc", autoComplete: "description", fullWidth: true, required: true, error: !!formik.errors.dlc && formik.touched.dlc, helperText: formik.errors.dlc && formik.touched.dlc
                                    ? formik.errors.dlc
                                    : '', ...formik.getFieldProps('dlc') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "price", children: "Price ($)" }), _jsx(TextField, { id: "price", type: "number", variant: "filled", placeholder: "Template price", autoComplete: "price", fullWidth: true, required: true, error: !!formik.errors.price && formik.touched.price, helperText: formik.errors.price && formik.touched.price
                                    ? formik.errors.price
                                    : '', ...formik.getFieldProps('price') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "specificCountrySellingOption", children: "Specific Country Selling Option" }), _jsx(SelectMenu, { id: "specificCountrySellingOption", value: formik.values.specificCountrySellingOption._id
                                    ? sellingOptions.find((item) => item._id === formik.values.specificCountrySellingOption._id) ?? { _id: null, name: "--Choose--" }
                                    : formik.values.specificCountrySellingOption, handleChange: handleSelectChange, options: [{ _id: null, name: "--Choose--" }, ...sellingOptions] })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "Language", children: "Language" }), languages.map((language) => (_jsxs(Box, { display: "flex", alignItems: "center", gap: "10px", children: [_jsx(Checkbox, { inputProps: { 'aria-label': language }, checked: formik.values.languages.includes(language), onChange: (e) => handleCheckboxChange(language, e.target.checked) }), _jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "language", children: language })] }, language)))] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "videos", children: "Add Video" }), _jsx(TextField, { id: "videos", type: "text", variant: "filled", placeholder: "Template videos", autoComplete: "description", fullWidth: true, required: true, error: !!formik.errors.videos && formik.touched.videos, helperText: formik.errors.videos && formik.touched.videos
                                    ? formik.errors.videos
                                    : '', ...formik.getFieldProps('videos'), onChange: (e) => formik.setFieldValue("videos", [e.target.value]) })] }), _jsx(Box, { children: _jsx(FileInputWithPreview, { label: 'Add Image', getFile: (file) => formik.setFieldValue('image', [file]), maxFiles: 5, existingFile: templateDetails.images }) }), _jsx(Box, { children: _jsxs(Accordion, { children: [_jsx(AccordionSummary, { expandIcon: _jsx(ExpandMoreIcon, {}), children: _jsx(Typography, { children: "Details" }) }), _jsxs(AccordionDetails, { children: [_jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "details.title", children: "Title" }), _jsx(TextField, { id: "details.title", type: "text", variant: "filled", placeholder: "Title", autoComplete: "details.title", fullWidth: true, required: true, error: !!formik.errors.details?.title && formik.touched.details?.title, helperText: formik.errors.details?.title && formik.touched.details?.title
                                                        ? formik.errors.details?.title
                                                        : '', ...formik.getFieldProps('details.title') })] }), _jsxs(Box, { children: [_jsx(InputLabel, { component: "label", sx: { fontSize: '12px', marginBottom: '20px' }, size: "small", htmlFor: "details.description", children: "Description" }), _jsx(Editor, { apiKey: 'kib4z0a5ef24utq20ohc2sqvodd58y3u1mdswdety295un2h', onInit: (_evt, editor) => editorRef.current = editor, onBlur: () => formik.setFieldValue("details.description", editorRef.current?.getContent()), initialValue: formik.values.details.description, init: {
                                                        height: 500,
                                                        menubar: false,
                                                        plugins: [
                                                            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                                                            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                                                            'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount'
                                                        ],
                                                        toolbar: 'undo redo | blocks | ' +
                                                            'bold italic forecolor | alignleft aligncenter ' +
                                                            'alignright alignjustify | bullist numlist outdent indent | ' +
                                                            'removeformat | help',
                                                        content_style: `
                                            body {
                                                font-family: Helvetica, Arial, sans-serif;
                                                font-size: 14px;
                                                background-color: transparent; /* Make background transparent */
                                            }
                                        `
                                                    } })] }), _jsxs(Stack, { direction: "column", mt: 4, gap: 1, children: [_jsx(Typography, { variant: "h5", fontWeight: 600, children: "SEO" }), _jsx(TextField, { id: "details.seo.metaTitle", type: "text", variant: "filled", placeholder: "Title", autoComplete: "details.seo.metaTitle", fullWidth: true, required: true, error: !!formik.errors.details?.seo?.metaTitle && formik.touched.details?.seo?.metaTitle, helperText: formik.errors.details?.seo?.metaTitle && formik.touched.details?.seo?.metaTitle
                                                        ? formik.errors.details?.seo?.metaTitle
                                                        : '', ...formik.getFieldProps('details.seo.metaTitle') }), _jsx(TextField, { id: "details.seo.metaDescription", type: "text", variant: "filled", placeholder: "Description", autoComplete: "details.seo.metaDescription", fullWidth: true, required: true, error: !!formik.errors.details?.seo?.metaDescription && formik.touched.details?.seo?.metaDescription, helperText: formik.errors.details?.title && formik.touched.details?.seo?.metaDescription
                                                        ? formik.errors.details?.seo?.metaDescription
                                                        : '', ...formik.getFieldProps('details.seo.metaDescription') }), _jsx(TextField, { id: "details.seo.metaKeywords", type: "text", variant: "filled", placeholder: "Keywords", autoComplete: "details.seo.metaKeywords", fullWidth: true, required: true, error: !!formik.errors.details?.seo?.metaKeywords && formik.touched.details?.seo?.metaKeywords, helperText: formik.errors.details?.seo?.metaKeywords && formik.touched.details?.seo?.metaKeywords
                                                        ? formik.errors.details?.seo?.metaKeywords
                                                        : '', ...formik.getFieldProps('details.seo.metaKeywords') })] })] })] }) }), _jsx(Button, { type: "submit", variant: "contained", size: "medium", fullWidth: true, disabled: !formik.isValid || formik.isSubmitting, children: "Submit" })] })] }));
}
