import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import * as React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, } from '@mui/material';
import Paper from '@mui/material/Paper';
import { uploadFormat } from 'pages/category/const/uploadFormat.ts';
import Typography from '@mui/material/Typography';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
export default function BasicModal({ open, setOpen }) {
    const handleClose = () => {
        setOpen(false);
    };
    return (_jsx(React.Fragment, { children: _jsxs(Dialog, { open: open, onClose: handleClose, "aria-labelledby": "alert-dialog-title", "aria-describedby": "alert-dialog-description", children: [_jsx(DialogActions, { children: _jsx(IconifyIcon, { icon: "mingcute:close-line", onClick: handleClose }) }), _jsx(DialogTitle, { id: "alert-dialog-title", children: "Bulk Category Upload" }), _jsx(DialogContent, { sx: { padding: 0 }, children: _jsx(TableContainer, { component: Paper, children: _jsxs(Table, { sx: { width: '100%' }, "aria-label": "simple table", children: [_jsx(TableHead, { children: _jsxs(TableRow, { children: [_jsx(TableCell, { width: "50%", children: "Field" }), _jsx(TableCell, { width: "50%", children: "Description" })] }) }), _jsx(TableBody, { children: uploadFormat.map((row) => (_jsxs(TableRow, { sx: { '&:last-child td, &:last-child th': { border: 0 } }, children: [_jsx(TableCell, { component: "th", scope: "row", children: row.field }), _jsxs(TableCell, { children: [_jsxs(Typography, { variant: "body2", children: [_jsx("strong", { children: "Type:" }), " ", row.type] }), _jsxs(Typography, { variant: "body2", children: [_jsx("strong", { children: "Required:" }), " ", row.required] }), _jsxs(Typography, { variant: "body2", children: [_jsx("strong", { children: "Example:" }), " ", row.example] })] })] }, row.field))) })] }) }) })] }) }));
}
