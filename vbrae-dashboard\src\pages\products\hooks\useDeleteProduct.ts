import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { deleteProduct } from 'pages/products/api/deleteProduct.ts';

export default function useDeleteProduct(props: { _id: string }) {
  const queryClient = useQueryClient();
  const { refetch: deleteRefetch, loading: deleteLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['delete-product', { ...props }],
      queryFn: () => deleteProduct(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['all-products']).finally(),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { deleteRefetch, deleteLoading };
}