import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getQuestionCategories } from 'pages/helpQuestions/api/getQuestionCategories.ts';

export default function useQuestionsCategories() {
  const { data: category, loading: categoryLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['questions'],
      queryFn: () => getQuestionCategories(),
      onError: showError,
    }),
    transform: (data) => data,
  });
  return { category, categoryLoading };
}
