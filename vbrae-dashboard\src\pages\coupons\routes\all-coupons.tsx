import { TableComponent } from 'components/table/table.tsx';
import { useEffect, useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { InputBase, InputLabel } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import Paper from '@mui/material/Paper';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import FeeModal from 'components/modals/FeeModal.tsx';
import useUpdateProduct from 'pages/products/hooks/useUpdateProduct.ts';
import SelectMenu, { SelectedValue } from 'components/select-menu.tsx';
import { useNavigate } from 'react-router-dom';
import useAllCoupons from 'pages/coupons/hooks/useAllCoupons.ts';
import { CouponDto } from 'pages/coupons/types/coupon.ts';
import Button from '@mui/material/Button';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import Chip from '@mui/material/Chip';
import { formatDate } from 'functions/time.ts';
import { statusOptions } from 'pages/coupons/const/statusOptions.ts';
import TextField from '@mui/material/TextField';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs from 'dayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import useDeleteCoupon from 'pages/coupons/hooks/useDeleteCoupon.ts';

export interface UpdateProps {
  [key: string]: string | boolean;
}

export default function AllCoupons() {

    const navigate = useNavigate();

    const [searchValue, setSearchValue] = useState('');
    const [open, setOpen] = useState({value: false, _id: "", price: '1'});
    const [selectedStatus, setSelectedStatus] = useState<SelectedValue>(statusOptions[0]);
    const [discountValue, setDiscountValue] = useState('');
    const [code, setCode] = useState('');
    const [createdAtState, setCreatedAtState] = useState('');
    const [reset, setReset] = useState(true);

    const [productToUpdate, setProductToUpdate] = useState<UpdateProps>({_id: ""});
    const [couponToDelete, setCouponToDelete] = useState({_id: ""});

    const queryParams = [
        selectedStatus?._id !== null && `isActive=${selectedStatus._id}`,
        discountValue && `discountValue=${discountValue}`,
        code && `code=${code}`,
        createdAtState && `createdAt=${createdAtState}`,
    ]
        .filter(Boolean)
        .join("&");

    const { coupons, couponsLoading, couponsRefetch } = useAllCoupons({query: queryParams});
    const {updateRefetch} = useUpdateProduct(productToUpdate);
    const {deleteRefetch} = useDeleteCoupon(couponToDelete);

    useEffect(() => {
        if(!productToUpdate._id) return;

        updateRefetch().finally(()=> {
            setProductToUpdate({_id: ""});
            couponsRefetch().finally();
        })
    }, [productToUpdate]);

    useEffect(() => {
        if(!couponToDelete._id) return;

        deleteRefetch().finally(() => {
          setCouponToDelete({ _id: '' });
          couponsRefetch().finally()
        });
    }, [couponToDelete])

    useEffect(() => {
        if(reset) couponsRefetch().finally()
    }, [reset]);

    const columns = useMemo<ColumnDef<CouponDto>[]>(
        () => [
            {
                header: 'Coupon Code',
                accessorFn: (row) => row.code,
                id: 'code',
                enableGlobalFilter: true,
            },
            {
                header: 'Discount Rate',
                accessorFn: (row) => row.discountValue,
                id: 'discountValue',
                cell: (info) => info.getValue(),
                enableGlobalFilter: true,
            },
            {
                header: 'No. of coupons',
                accessorFn: (row) => row.totalCoupons,
                id: 'totalCoupons',
                cell: (info) => info.getValue(),
                enableGlobalFilter: true,
            },
            {
                header: 'Expiry Date',
                accessorFn: (row) => row.expirationDate,
                id: 'expirationDate',
                cell: (info) => <span>{formatDate(info.row.original.expirationDate)}</span>,
                enableGlobalFilter: true,
            },
            {
                header: 'Status',
                accessorFn: (row) => row.isActive,
                id: 'isActive',
                cell: (info) =>  <Chip sx={{padding: '0 16px', fontSize: '12px', borderRadius: '20px'}} label={info.row.original.isActive ? 'Active' : 'Paused'} color="primary" />,
                enableGlobalFilter: true,
            },
            {
                header: 'Date',
                accessorFn: (row) => row.createdAt,
                id: 'createdAt',
                cell: (info) => <span>{formatDate(info.row.original.createdAt)}</span>,
                enableGlobalFilter: true,
            },
            {
                header: 'Action',
                accessorFn: () => {},
                id: 'action',
                cell: (info) => (
                    <Stack direction="row" spacing={1} alignItems="center">
                        <Button
                            onClick={() => navigate(`/coupon/${info.row.original._id}`)}
                            sx={{ padding: 0, minWidth: 0 }}
                        >
                            <IconifyIcon icon="ion:create-outline" sx={{ fontSize: '20px' }} />
                        </Button>
                        <Button sx={{ padding: 0, minWidth: 0 }} onClick={()=> setCouponToDelete({_id: info.row.original._id})}>
                            <IconifyIcon
                                icon="ion:trash-outline"
                                sx={{ fontSize: '20px' }}
                            />
                        </Button>
                    </Stack>
                ),
                enableGlobalFilter: false, // Actions don't need to be globally searchable
            },
        ],
        [],
    );

    const handleSelectChange = (_ : string, value?: SelectedValue) => {
        setSelectedStatus(value ?? {_id: "", name: ""})
    }

    const resetFilters = () => {
        setSelectedStatus(statusOptions[0]);
        setCreatedAtState('');
        setCode('');
        setDiscountValue('');
        setReset(true);
    }

    if (couponsLoading || !coupons) return <CircularProgress />;
    return (
        <Stack direction="column" spacing={2}>
            <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
                <Typography
                    variant="h5"
                    fontWeight={600}
                    letterSpacing={1}
                    fontFamily={fontFamily.workSans}
                    display={{ xs: 'none', lg: 'block' }}
                >
                    All Coupons
                </Typography>
                <Button type="submit" variant="contained" size="small" onClick={()=> navigate(`/coupons/add`)}>
                    Add Coupon
                </Button>
            </Stack>

            <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
                <Stack direction="row" gap={2} alignItems="end">
                    <Box width={{xs: "100%", sm:"160px"}}>
                        <InputLabel
                            component="label"
                            sx={{ fontSize: '12px', marginBottom: '20px' }}
                            size="small"
                            htmlFor="Status"
                        >
                            Status
                        </InputLabel>
                        <SelectMenu
                            value={selectedStatus}
                            id="status"
                            handleChange={handleSelectChange}
                            options={statusOptions}
                        />
                    </Box>
                    <Box width={{xs: "100%", sm:"160px"}}>
                        <InputLabel
                            component="label"
                            sx={{ fontSize: '12px', marginBottom: '20px' }}
                            size="small"
                            htmlFor="discountValue"
                        >
                            Discount Rate
                        </InputLabel>
                        <TextField
                            id="discountValue"
                            type="number"
                            variant="filled"
                            placeholder="E.g : 5"
                            autoComplete="discountValue"
                            fullWidth
                            value={discountValue}
                            onChange={(e)=> setDiscountValue(e.target.value)}
                        />
                    </Box>
                    <Box width={{xs: "100%", sm:"160px"}}>
                        <InputLabel
                            component="label"
                            sx={{ fontSize: '12px', marginBottom: '20px' }}
                            size="small"
                            htmlFor="code"
                        >
                            Coupon Code
                        </InputLabel>
                        <TextField
                            id="code"
                            type="text"
                            variant="filled"
                            placeholder="Code"
                            autoComplete="code"
                            fullWidth
                            value={code}
                            onChange={e=> setCode(e.target.value)}
                        />
                    </Box>
                    <Box>
                        <InputLabel
                            component="label"
                            sx={{ fontSize: '12px', marginBottom: '20px' }}
                            size="small"
                            htmlFor="createdAt"
                        >
                            Creation Date
                            <LocalizationProvider dateAdapter={AdapterDayjs}>
                                <DatePicker
                                    sx={{
                                        display: "block",
                                        "& .MuiInputBase-root": {
                                            width: "100%",
                                        },
                                        "& fieldset": {
                                            border: "none",
                                        }
                                    }}
                                    label={null}
                                    value={createdAtState ? dayjs(createdAtState) : null}
                                    onChange={newValue=> setCreatedAtState(newValue ? newValue.format("YYYY-MM-DD") : '')}
                                />
                            </LocalizationProvider>
                        </InputLabel>
                    </Box>
                    <Button variant="contained" size="medium" onClick={()=> {
                        couponsRefetch().finally();
                        setReset(false)
                    }}>
                        Filter
                    </Button>
                    {!reset && <Button variant="contained" size="medium" onClick={()=> resetFilters()}>
                        Reset
                    </Button>}
                </Stack>

                <Box>
                    <Paper
                        component="form"
                        sx={{
                            p: '2px 4px',
                            display: 'flex',
                            alignItems: 'end',
                            justifyContent: 'center',
                            width: { xs: '100%', sm: 300 },
                        }}
                    >
                        <InputBase
                            sx={{ ml: 1, flex: 1, border: 'none' }}
                            placeholder="Search here"
                            inputProps={{ 'aria-label': 'search google maps' }}
                            onChange={(e) => setSearchValue(e.target.value.trim())}
                        />
                        <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
                            <SearchIcon />
                        </IconButton>
                    </Paper>
                </Box>
            </Stack>

            <Box sx={{ overflowX: 'auto', width: '100%' }}>
                {couponsLoading && <CircularProgress />}
                <TableComponent
                    columns={columns}
                    data={coupons}
                    globalFilter={searchValue}
                    setGlobalFilter={setSearchValue}
                />
            </Box>
            <FeeModal open={open} setOpen={setOpen} />
        </Stack>
    );
}