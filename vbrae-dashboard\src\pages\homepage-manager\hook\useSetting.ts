import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getSettings } from 'pages/homepage-manager/api/getSettings.ts';

export default function useSetting() {
  const { data: settings, loading: settingLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['all-settings'],
      queryFn: () => getSettings(),
      onError: showError,
    }),
    transform: (data) => data.data,
  });
  return { settings, settingLoading };
}
