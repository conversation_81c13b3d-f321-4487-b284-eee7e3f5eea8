import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import ListItemButton from '@mui/material/ListItemButton';
import AvatarImage from 'assets/images/avater.png';
const ProfileListItem = ({ subheader, path }) => {
    return (_jsx(ListItemButton, { component: Link, href: path, children: _jsxs(Stack, { spacing: 1, alignItems: "center", children: [_jsx(Avatar, { src: AvatarImage, sx: { height: 36, width: 36, bgcolor: 'primary.main' } }), _jsxs(Stack, { direction: "column", children: [_jsx(Typography, { variant: "subtitle2", color: "text.primary", letterSpacing: 0.5, children: subheader }), _jsx(Typography, { variant: "caption", color: "text.secondary", fontWeight: 400, children: "Account Settings" })] })] }) }));
};
export default ProfileListItem;
