interface Address {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    _id: string;
}

interface Seller {
    currentOrder: string | null;
    _id: string;
    name: string;
    email: string;
    password: string;
    ipAddress: string;
    loginWithFacebook: boolean;
    loginWithGoogle: boolean;
    role: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
    address: Address[];
    paymentId: string;
}

export interface ShopDto {
    _id: string;
    shopName: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    location: string;
    shopDescription: string;
    tier: string;
    isApproved: string;
    seller: Seller;
    createdAt: string;
    updatedAt: string;
    __v: number;
}

interface Pagination {
    currentPage: number;
    pages: number;
}

export interface ApiShopResponse {
    status: string;
    results: number;
    pagination: Pagination;
    data: ShopDto[];
}
