import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useState } from 'react';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import FormControlLabel from '@mui/material/FormControlLabel';
import Checkbox from '@mui/material/Checkbox';
import IconifyIcon from 'components/base/IconifyIcon';
import useLoginForm from 'pages/authentication/hooks/useLoginForm.ts';
const Login = () => {
    const [showPassword, setShowPassword] = useState(false);
    const { formik } = useLoginForm();
    return (_jsxs(_Fragment, { children: [_jsx(Typography, { align: "center", variant: "h3", fontWeight: 600, children: "LogIn" }), _jsxs(Stack, { onSubmit: formik.handleSubmit, component: "form", direction: "column", gap: 2, mt: 4, children: [_jsx(TextField, { id: "email", type: "email", variant: "filled", placeholder: "Your Email", autoComplete: "email", fullWidth: true, autoFocus: true, required: true, ...formik.getFieldProps('email') }), _jsx(TextField, { id: "password", type: showPassword ? 'text' : 'password', variant: "filled", placeholder: "Your Password", autoComplete: "current-password", fullWidth: true, autoFocus: true, required: true, InputProps: {
                            endAdornment: (_jsx(InputAdornment, { position: "end", sx: { opacity: formik.values.password ? 1 : 0 }, children: _jsx(IconButton, { "aria-label": "toggle password visibility", onClick: () => setShowPassword(!showPassword), edge: "end", children: _jsx(IconifyIcon, { icon: showPassword ? 'ion:eye' : 'ion:eye-off' }) }) })),
                        }, ...formik.getFieldProps('password') }), _jsx(Stack, { mt: -1.5, alignItems: "center", justifyContent: "space-between", children: _jsx(FormControlLabel, { onChange: (e) => formik.setFieldValue('rememberMe', e.target.checked), control: _jsx(Checkbox, { id: "checkbox", name: "checkbox", color: "primary" }), label: "Remember me" }) }), _jsx(Button, { type: "submit", variant: "contained", size: "medium", fullWidth: true, children: "Submit" })] })] }));
};
export default Login;
