import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { deleteCoupon } from 'pages/coupons/api/deleteCoupon.ts';

export default function useDeleteCoupon(props: { _id: string }) {
  const { refetch: deleteRefetch, loading: deleteLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['delete-coupon', { ...props }],
      queryFn: () => deleteCoupon(props),
      onError: showError,
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { deleteRefetch, deleteLoading };
}