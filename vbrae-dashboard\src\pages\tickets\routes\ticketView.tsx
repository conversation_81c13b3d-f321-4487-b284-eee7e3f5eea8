import { useNavigate } from 'react-router-dom';
import { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import BasicDropdown from 'components/dropdown.tsx';
import CircularProgress from '@mui/material/CircularProgress';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { fontFamily } from 'theme/typography.ts';
import Box from '@mui/material/Box';
import { InputBase } from '@mui/material';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import { TableComponent } from 'components/table/table.tsx';
import SearchIcon from '@mui/icons-material/Search';
import useTicket from 'pages/tickets/hooks/useTicket.ts';
import { TicketDto } from 'pages/tickets/types/Ticket.ts';

export default function TicketView() {
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState('');
  const { tickets, ticketsLoading } = useTicket();

  const columns = useMemo<ColumnDef<TicketDto>[]>(
    () => [
      {
        header: 'Name',
        accessorFn: (row) => row.name,
        id: 'name',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Email',
        accessorFn: (row) => row.email,
        id: 'email',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Subject',
        accessorFn: (row) => row.subject,
        id: 'subject',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Description',
        accessorFn: (row) => row.description,
        id: 'description',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Status',
        accessorFn: (row) => row.status,
        id: 'status',
        cell: (info) => <span>{info.row.original.status.toUpperCase()}</span>,
        enableGlobalFilter: true,
      },
      {
        header: 'Attachments',
        accessorFn: (row) => row.attachments,
        id: 'attachments',
        cell: (info) => <span>{info.row.original.attachments?.length}</span>,
      },
      {
        header: 'Actions',
        accessorFn: () => {},
        id: 'actions',
        cell: (info) => {
          const options = [
            {
              title: 'View Details',
              action: () => navigate(`/tickets/${info.row.original._id}?conversationId=${info.row.original.conversationId}`),
            },
          ];
          return <BasicDropdown options={options} />;
        },
        enableGlobalFilter: false,
      },
    ],
    [],
  );

  if (ticketsLoading || !tickets) return <CircularProgress />;

  return (
    <>
      <Stack direction="column" spacing={2}>
        <Typography
          variant="h5"
          fontWeight={600}
          letterSpacing={1}
          fontFamily={fontFamily.workSans}
          display={{ xs: 'none', lg: 'block' }}
        >
          Tickets
        </Typography>
      </Stack>

      <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
        <Box>
          <Paper
            component="form"
            sx={{
              p: '2px 4px',
              display: 'flex',
              alignItems: 'end',
              justifyContent: 'center',
              width: { xs: '100%', sm: 300 },
            }}
          >
            <InputBase
              sx={{ ml: 1, flex: 1, border: 'none' }}
              placeholder="Search here"
              inputProps={{ 'aria-label': 'search' }}
              onChange={(e) => setSearchValue(e.target.value.trim())}
            />
            <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
              <SearchIcon />
            </IconButton>
          </Paper>
        </Box>
      </Stack>

      <Box sx={{ overflowX: 'auto', width: '100%' }}>
        <TableComponent
          columns={columns}
          data={tickets}
          globalFilter={searchValue}
          setGlobalFilter={setSearchValue}
        />
      </Box>
    </>
  );
}