import React from "react";

interface FileViewerProps {
    fileUrl: string;
}

const FileViewer: React.FC<FileViewerProps> = ({ fileUrl }) => {
    const getFileType = (url: string): string => {
        return url.split(".").pop()?.toLowerCase() || "";
    };

    const renderFile = () => {
        const fileType = getFileType(fileUrl);

        switch (fileType) {
            case "pdf":
                return <embed src={fileUrl} type="application/pdf" width="100%" height="600px" />;
            case "doc":
            case "docx":
                return (
                    <object
                        data={`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(fileUrl)}`}
        type="text/html"
        width="100%"
        height="600px"
            />
    );
    case "txt":
        return <iframe src={fileUrl} title="Text File" width="100%" height="600px" />;
    case "jpg":
    case "jpeg":
    case "png":
    case "gif":
        return <img src={fileUrl} alt="File" style={{ maxWidth: "100%" }} />;
    default:
        return <p>Unsupported file type: {fileType}</p>;
    }
    };

    return <div>{renderFile()}</div>;
};

export default FileViewer;
