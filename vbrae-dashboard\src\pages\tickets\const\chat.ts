import { styled } from '@mui/system';
import { Box, Paper } from '@mui/material';

export const InputContainer = styled(Box)({
  padding: '16px',
  borderTop: '1px solid rgba(0, 0, 0, 0.12)',
  display: 'flex',
  flexDirection: 'column',
  gap: '10px',
});

export const FilePreviewContainer = styled(Box)({
  display: 'flex',
  flexWrap: 'wrap',
  gap: '8px'
});


export const ChatContainer = styled(Paper)(() => ({
  display: 'flex',
  flexDirection: 'column',
  height: '80vh',
  margin: '20px 0',
  overflow: 'hidden',
}));

export const MessageList = styled(Box)({
  flex: 1,
  overflowY: "auto",
  padding: "20px",
  display: "flex",
  flexDirection: "column",
  gap: "10px",

  // Optional custom scrollbar styling
  scrollbarWidth: "thin",
  scrollbarColor: "#c1c1c1 transparent",

  "&::-webkit-scrollbar": {
    width: "6px",
  },
  "&::-webkit-scrollbar-thumb": {
    backgroundColor: "#c1c1c1",
    borderRadius: "8px",
  },
  "&::-webkit-scrollbar-track": {
    backgroundColor: "transparent",
  },
});

export const MessageBubble = styled(Box)(() => ({
  maxWidth: "70%",
  padding: "12px",
  borderRadius: "12px",
  backgroundColor: "#388E3C",
  color: "#FFFFFF",
  alignSelf: "flex-start",
  position: "relative",
  wordBreak: "break-word",
}));

export const TicketHeader = styled(Box)({
  padding: "16px",
  borderBottom: "1px solid rgba(0, 0, 0, 0.12)",
  display: "flex",
  alignItems: "center",
  gap: "16px"
});


export const mockMessages = [
  {
    id: 1,
    content: "Hello! I'm having issues with my login credentials.",
    sender: "client",
    timestamp: new Date(2024, 0, 1, 10, 30),
    status: "read",
    attachments: [
      {
        name: "screenshot.png",
        url: "https://images.unsplash.com/photo-*************-727a05c4013d",
      },
    ]
  },
  {
    id: 2,
    content: "I'll help you with that. Can you please provide your account email?",
    sender: "admin",
    timestamp: new Date(2024, 0, 1, 10, 32),
    status: "read",
    attachments: []
  },
  {
    id: 3,
    content: "My <NAME_EMAIL>",
    sender: "client",
    timestamp: new Date(2024, 0, 1, 10, 33),
    status: "read",
    attachments: []
  }
];