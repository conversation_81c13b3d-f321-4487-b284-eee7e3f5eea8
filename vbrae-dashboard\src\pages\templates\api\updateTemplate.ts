import { patchRequest } from 'vbrae-utils';
import { TemplateDetails } from 'pages/templates/types/template.ts';

interface TemplateProps {
  coverImage?: string;
  templateName: string;
  slug: string;
  listingType: string;
  category: string;
  subcategory: string;
  region: string;
  genres: string;
  releaseDate: string;
  preOrder: boolean;
  dlc: boolean;
  specificCountrySellingOption: boolean;
  languages: string[];
  videos: string[];
  images: string[];
  details: TemplateDetails;
  _id: string;
}

export async function updateTemplate(props: TemplateProps): Promise<{ message: string } | undefined> {
  const r = await patchRequest<TemplateProps>({
    url: `templates/${props._id}`,
    data: props,
    useAuth: true,
  });
  return r.response;
}