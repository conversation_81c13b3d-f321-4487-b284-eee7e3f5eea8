import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getBannerDetails } from 'pages/homepage-manager/api/getBannerDetails.ts';

export default function useBannerDetails({ _id }: {_id?: string}) {
  const { data: bannerDetails, loading: bannerDetailsLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['banners-details', _id],
      queryFn: () => getBannerDetails({ _id: _id! }),
      onError: showError,
      enabled: !!_id
    }),
    transform: (data) => data.data.banner,
  });
  return { bannerDetails, bannerDetailsLoading };
}
