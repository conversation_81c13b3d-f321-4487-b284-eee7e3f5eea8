export interface BlogCategory {
    _id: string;
    language: string;
    categoryName: string;
    description: string;
    keywords: string[];
    order: number;
    createdAt: string; // ISO date string
    updatedAt: string; // ISO date string
    slug: string;
    __v: number;
}

export interface BlogCategoryResponse {
    status: string;
    results: number;
    pagination: Record<string, unknown>;
    data: BlogCategory[];
}
