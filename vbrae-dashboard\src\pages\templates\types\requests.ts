interface Seller {
    _id: string;
    name: string;
    email: string;
}

export interface RequestDto {
    _id: string;
    title: string;
    region: string;
    category: {
        categoryName: {
            en: string;
        }
    };
    language: string[];
    additionalInformation: string;
    isApproved: string;
    seller: Seller;
    createdAt: string;
    updatedAt: string;
    __v: number;
}