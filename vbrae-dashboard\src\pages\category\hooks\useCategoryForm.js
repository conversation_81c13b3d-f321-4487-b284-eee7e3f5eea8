import { useMutation } from 'react-query';
import * as Yup from 'yup';
import { useFormik } from 'formik';
import { showError, showSuccess } from 'vbrae-utils';
import { postCategory } from 'pages/category/api/postCategory.ts';
import { useEffect, useState } from "react";
import { updateCategory } from "pages/category/api/updateCategory.ts";
import { uploadUrl } from "pages/category/api/uploadUrl.ts";
const subCategorySchema = Yup.object().shape({
    categoryName: Yup.object().shape({
        en: Yup.string().required(),
        fr: Yup.string().trim(),
        it: Yup.string().trim(),
        de: Yup.string().trim(),
        es: Yup.string().trim(),
    }),
    metaTitle: Yup.string()
        .min(3, 'Minimum 3 characters')
        .required('Meta Title is required'),
    metaDescription: Yup.string()
        .min(3, 'Minimum 3 characters')
        .required('Meta Description is required'),
    metaKeywords: Yup.string().required('Meta Keywords are required'),
    category_order: Yup.number()
        .required('Order is required')
        .positive('Order must be a positive number'),
    visibility: Yup.boolean().required('Visibility is required'),
    showInMainMenu: Yup.boolean().required('Show in Main Menu is required'),
    showImageOnMainMenu: Yup.boolean().required('Show Image on Main Menu is required'),
    image: Yup.mixed().nullable(),
    category: Yup.object().shape({
        _id: Yup.mixed().nullable(),
        name: Yup.string().optional(),
    }),
});
export default function useCategoryForm({ categoryDetails }) {
    const [initialValues, setInitialValues] = useState({
        categoryName: {
            en: '',
            fr: '',
            it: '',
            de: '',
            es: '',
        },
        metaTitle: '',
        metaDescription: '',
        metaKeywords: '',
        category_order: 1,
        visibility: true,
        showInMainMenu: true,
        showImageOnMainMenu: false,
        image: null,
        category: { _id: null, name: "None" },
    });
    useEffect(() => {
        if (!categoryDetails) return;

        setInitialValues({
            categoryName: {
                en: categoryDetails.categoryName.en,
                fr: categoryDetails.categoryName.fr,
                it: categoryDetails.categoryName.it,
                de: categoryDetails.categoryName.de,
                es: categoryDetails.categoryName.es,
            },
            metaTitle: categoryDetails.metaTitle,
            metaDescription: categoryDetails.metaDescription,
            metaKeywords: categoryDetails.metaKeywords,
            category_order: categoryDetails.category_order,
            visibility: categoryDetails.visibility,
            showInMainMenu: categoryDetails.showInMainMenu,
            showImageOnMainMenu: categoryDetails.showImageOnMainMenu,
            image: categoryDetails.image,
            category: { _id: categoryDetails.parent_id, name: categoryDetails.parent_id ?? "None" },
        });
    }, [categoryDetails]);
    const { mutateAsync: updateASync } = useMutation(updateCategory, {
        onError: (error) => showError(error),
    });
    const { mutateAsync } = useMutation(postCategory, {
        onError: (error) => showError(error),
    });
    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: subCategorySchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            let response;
            let imageUrl = null;
            if (values.image instanceof File) {
                const fileType = values.image.name.split('.').pop()?.toLowerCase() || "unknown";
                const resignedResponse = await uploadUrl({ name: values.image.name, fileType });
                const { url: resignedUrl, path: filePath } = resignedResponse;
                await fetch(resignedUrl, {
                    method: "PUT",
                    headers: { "Content-Type": values.image.type, "x-amz-acl": "public-read" },
                    body: values.image,
                });
                imageUrl = filePath;
            }
            if (!categoryDetails)
                response = await mutateAsync({ ...values, parent_id: values.category._id, image: imageUrl });
            if (categoryDetails)
                response = await updateASync({ ...values, parent_id: values.category._id, _id: categoryDetails._id, image: imageUrl });
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess(response.message);
            }
        },
    });
    return { formik };
}
