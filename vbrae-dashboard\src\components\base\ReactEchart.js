import { jsx as _jsx } from "react/jsx-runtime";
import { forwardRef } from 'react';
import { Box } from '@mui/material';
import ReactEChartsCore from 'echarts-for-react/lib/core';
const ReactEchart = forwardRef(({ option, ...rest }, ref) => {
    return (_jsx(Box, { component: ReactEChartsCore, ref: ref, option: {
            ...option,
            tooltip: {
                ...option.tooltip,
                confine: true,
            },
        }, ...rest }));
});
export default ReactEchart;
