import { patchRequest } from 'vbrae-utils';

interface UpdateOrderStatusResponse {
  status: string;
  message: string;
}

export async function updateOrderStatus({
  orderId,
  status
}: {
  orderId: string;
  status: string;
}): Promise<UpdateOrderStatusResponse> {
  const result = await patchRequest({
    url: `order/update-status/${orderId}`,
    data: { status },
    useAuth: true,
  });

  return result.response;
}
