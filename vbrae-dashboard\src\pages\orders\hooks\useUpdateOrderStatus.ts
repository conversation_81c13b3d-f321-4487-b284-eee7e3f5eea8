import { useMutation, useQueryClient } from 'react-query';
import { updateOrderStatus } from 'pages/orders/api/updateOrderStatus.ts';
import { showError } from 'vbrae-utils';
import Swal from 'sweetalert2';

interface ErrorType {
  message: string;
}

interface UpdateOrderStatusParams {
  orderId: string;
  status: string;
}

export default function useUpdateOrderStatus() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: ({ orderId, status }: UpdateOrderStatusParams) =>
      updateOrderStatus({ orderId, status }),
    onSuccess: () => {
      // Invalidate and refetch orders data
      queryClient.invalidateQueries(['all-orders']);
      
      Swal.fire({
        title: 'Success!',
        text: 'Order status updated successfully',
        icon: 'success',
        background: '#171E2E',
        confirmButtonColor: '#2DC071',
      });
    },
    onError: (error: ErrorType) => {
      showError(error);
    },
  });

  const updateStatus = async (orderId: string, status: string) => {
    const result = await Swal.fire({
      title: 'Complete Order?',
      text: 'Are you sure you want to mark this order as completed?',
      icon: 'question',
      showCancelButton: true,
      background: '#171E2E',
      confirmButtonColor: '#2DC071',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, complete it!',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      mutation.mutate({ orderId, status });
    }
  };

  return {
    updateStatus,
    isLoading: mutation.isLoading,
  };
}
