import * as Yup from 'yup';
import { useMutation } from 'react-query';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { showError, showSuccess } from 'vbrae-utils';
import { GiveawayAction, GiveawayDTO } from 'pages/give-away/types/give-away.ts';
import { postGiveAway } from 'pages/give-away/api/postGiveAway.ts';
import { patchGiveAway } from 'pages/give-away/api/patchGiveAway.ts';

export type ErrorType = {
  message: string;
};

const giveawaySchema = Yup.object().shape({
  title: Yup.string().trim().required('Title is required'),
  description: Yup.string().required('Description is required'),
  startDate: Yup.date().required('Start date is required'),
  endDate: Yup.date()
    .required('End date is required')
    .min(Yup.ref('startDate'), 'End date must be after start date'),
  prizes: Yup.array().of(Yup.string().required('Prize cannot be empty')).min(1, 'At least one prize is required'),
  actions: Yup.array()
    .of(
      Yup.object().shape({
        type: Yup.string().required('Action type is required'),
        label: Yup.string().required('Label is required'),
        points: Yup.number().required('Points are required').min(1),
        url: Yup.string().url('Enter a valid URL').required('URL is required'),
      })
    )
    .min(1, 'At least one action is required'),
});

export type InitialGiveawayValues = {
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  prizes: string[];
  actions: GiveawayAction[];
};

export default function useGiveawayForm({ existingGiveaway }: { existingGiveaway?: GiveawayDTO }) {
  const [initialValues, setInitialValues] = useState<InitialGiveawayValues>({
    title: '',
    description: '',
    startDate: '',
    endDate: '',
    prizes: [''],
    actions: [
      {
        type: '',
        label: '',
        points: 1,
        url: '',
      },
    ],
  });

  useEffect(() => {
    if (!existingGiveaway) return;

    setInitialValues({
      title: existingGiveaway.title,
      description: existingGiveaway.description ?? '',
      startDate: existingGiveaway.startDate.split("T")[0],
      endDate: existingGiveaway.endDate.split("T")[0],
      prizes: existingGiveaway.prizes || [''],
      actions: existingGiveaway.actions || [],
    });
  }, [existingGiveaway]);

  const { mutateAsync } = useMutation(postGiveAway, {
    onError: (error: ErrorType) => showError(error),
  });

  const { mutateAsync: updateAsync } = useMutation(patchGiveAway, {
    onError: (error: ErrorType) => showError(error),
  });

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    validationSchema: giveawaySchema,
    onSubmit: async (values, { setSubmitting, resetForm }) => {
      setSubmitting(true);
      let response;
      if (existingGiveaway) {
        response = await updateAsync({ _id: existingGiveaway._id, ...values });
      } else {
        response = await mutateAsync(values);
      }
      setSubmitting(false);
      !existingGiveaway && resetForm();
      if (response) {
        showSuccess(`Giveaway ${existingGiveaway ? 'updated' : 'added'} successfully`);
      }
    },
  });

  return { formik };
}