import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import CircularProgress from '@mui/material/CircularProgress';
import useQuestionsCategories from 'pages/helpQuestions/hooks/useQuestionsCategories.ts';
import QuestionAccordion from 'components/accordion/question-accordion.tsx';
import { AccordionCategoryDto } from 'pages/category/types/accordion.ts';
import QuestionSubCategoryModal from 'components/modals/QuestionSubcategoryModal.tsx';

export default function HelpQuestionView() {
  const navigate = useNavigate();

  const { category, categoryLoading } = useQuestionsCategories();

  const [open, setOpen] = useState(false);
  const [categoryId, setCategoryId] = useState('');
  const [accordionData, setAccordionData] = useState<AccordionCategoryDto[]>([]);

  useEffect(() => {
    if (!category) return;

    setAccordionData(category);
  }, [category]);

  return (
    <Stack spacing={2} direction="column">
      <QuestionSubCategoryModal
        open={open}
        handleClose={() => setOpen(false)}
        state={{ _id: categoryId }}
      />
      <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
          <Typography variant="h5" fontWeight={600}>
            Help Questions
          </Typography>
        </Stack>

        {categoryLoading || !category ? (
          <CircularProgress />
        ) : (
          <>
            {category?.length > 0 ? (
              <QuestionAccordion
                data={accordionData}
                onParentDelete={() => {}}
                onChildDelete={() => {}}
                onAdd={({ _id }) => {
                  setCategoryId(_id);
                  setOpen(true);
                }}
                onParentEdit={({ _id, parentId }) =>
                  navigate(`/helpQuestions/${parentId}/subCategory/${_id}`)
                }
                onChildEdit={({ _id, parentId }) =>
                  navigate(`/helpQuestions/${parentId}/subCategory/${_id}`)
                }
              />
            ) : (
              <Typography
                variant="body1"
                sx={{
                  fontWeight: 'bold',
                  fontSize: 16,
                  textAlign: 'center',
                  marginTop: 2,
                }}
              >
                Nothing here yet!
              </Typography>
            )}
          </>
        )}
      </Paper>
    </Stack>
  );
}