import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getAllTicket } from 'pages/tickets/api/getAllTicket.ts';

export default function useTicket() {
  const { data: tickets, loading: ticketsLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['tickets'],
      queryFn: () => getAllTicket(),
      onError: showError,
    }),
    transform: (data) => data.data,
  });
  return { tickets, ticketsLoading };
}
