import { TableComponent } from 'components/table/table.tsx';
import { useMemo, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { InputBase } from '@mui/material';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import Paper from '@mui/material/Paper';
import { fontFamily } from 'theme/typography.ts';
import Typography from '@mui/material/Typography';
import Stack from '@mui/material/Stack';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import { Order } from 'pages/orders/types/order.ts';
import BasicDropdown from 'components/dropdown.tsx';
import useAllOrders from 'pages/orders/hooks/useAllOrders.ts';
import useUpdateOrderStatus from 'pages/orders/hooks/useUpdateOrderStatus.ts';

export default function AllOrders() {
    const [searchValue, setSearchValue] = useState('');

    const { orders, orderLoading } = useAllOrders({query: ''});
    const { updateStatus } = useUpdateOrderStatus();

    const columns = useMemo<ColumnDef<Order>[]>(
        () => [
            {
                header: 'Order ID',
                accessorFn: (row) => row._id,
                id: 'orderId',
                cell: (info) => (
                    <Typography variant="body2" noWrap>
                        {info.getValue()?.toString().slice(-8) || 'N/A'}
                    </Typography>
                ),
                enableGlobalFilter: true,
            },
            {
                header: 'User',
                accessorFn: (row) => row.userDetails?.name,
                id: 'name',
                cell: (info) => (
                    <Typography variant="body2">
                        {info.getValue()?.toString() || 'N/A'}
                    </Typography>
                ),
                enableGlobalFilter: true,
            },
            {
                header: 'Items',
                accessorFn: (row) => row.items.length,
                id: 'itemCount',
                cell: (info) => (
                    <Typography variant="body2">
                        {info.getValue()?.toString()} item(s)
                    </Typography>
                ),
                enableGlobalFilter: false,
            },
            {
                header: 'Total',
                accessorFn: (row) => row.total,
                id: 'total',
                cell: (info) => (
                    <Typography variant="body2" fontWeight={600}>
                        ${info.getValue()?.toString() || '0'}
                    </Typography>
                ),
                enableGlobalFilter: false,
            },
            {
                header: 'Status',
                accessorFn: (row) => row.status,
                id: 'status',
                cell: (info) => (
                    <Typography
                        variant="body2"
                        sx={{
                            textTransform: 'capitalize',
                            color: info.getValue() === 'completed' ? 'success.main' : 'warning.main'
                        }}
                    >
                        {info.getValue()?.toString() || 'N/A'}
                    </Typography>
                ),
                enableGlobalFilter: true,
            },
            {
                header: 'Date',
                accessorFn: (row) => row.createdAt,
                id: 'createdAt',
                cell: (info) => (
                    <Typography variant="body2">
                        {new Date(info.getValue()?.toString() || '').toLocaleDateString()}
                    </Typography>
                ),
                enableGlobalFilter: true,
            },
            {
                header: 'Actions',
                accessorFn: () => {},
                id: 'actions',
                cell: (info) => {
                    const isCompleted = info.row.original.status === 'completed';
                    const options = [
                        {
                            title: "Update Status",
                            action: () => updateStatus(info.row.original._id, 'completed'),
                            disabled: isCompleted
                        }
                    ];

                    return <BasicDropdown options={options} />
                },
                enableGlobalFilter: false,
            }
        ],
        [],
    );

    return (
        <Stack direction="column" spacing={2}>
            <Typography
                variant="h5"
                fontWeight={600}
                letterSpacing={1}
                fontFamily={fontFamily.workSans}
                display={{ xs: 'none', lg: 'block' }}
            >
                All Orders
            </Typography>

            <Stack direction="row" gap={2} justifyContent="space-between" alignItems="end">
                <Box>
                    <Paper
                        component="form"
                        sx={{
                            p: '2px 4px',
                            display: 'flex',
                            alignItems: 'end',
                            justifyContent: 'center',
                            width: { xs: '100%', sm: 300 },
                        }}
                    >
                        <InputBase
                            sx={{ ml: 1, flex: 1, border: 'none' }}
                            placeholder="Search orders..."
                            inputProps={{ 'aria-label': 'search orders' }}
                            onChange={(e) => setSearchValue(e.target.value.trim())}
                        />
                        <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
                            <SearchIcon />
                        </IconButton>
                    </Paper>
                </Box>
            </Stack>

            <Box sx={{ overflowX: 'auto', width: '100%' }}>
                {orderLoading && <CircularProgress />}
                <TableComponent
                    columns={columns}
                    data={orders || []}
                    globalFilter={searchValue}
                    setGlobalFilter={setSearchValue}
                />
            </Box>
        </Stack>
    );
}