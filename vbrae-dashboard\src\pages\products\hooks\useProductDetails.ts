import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getProductDetails } from 'pages/products/api/getProductDetails.ts';

export default function useProductDetails({_id}: {_id: string}) {
  const { data: product, loading: productLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['product-details', _id],
      queryFn: () => getProductDetails({_id}),
      onError: showError,
      enabled: !!_id
    }),
    transform: (data) => data.data,
  });
  return { product, productLoading };
}
