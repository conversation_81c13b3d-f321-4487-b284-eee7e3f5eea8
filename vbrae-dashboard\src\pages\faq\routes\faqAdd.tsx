import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { InputLabel } from '@mui/material';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import useFaqForm from 'pages/faq/hooks/useFaqForm.ts';

export default function FaqAdd() {
  const { formik } = useFaqForm({ existingFaq: undefined });

  return (
    <form onSubmit={formik.handleSubmit}>
      <Typography variant="h5" fontWeight={600} sx={{ fontSize: { xs: '16px', sm: '18px' } }}>
        Add Faq
      </Typography>

      <Box>
        <InputLabel
          component="label"
          sx={{ fontSize: '12px', marginBottom: '20px' }}
          size="small"
          htmlFor="title"
        >
            Title
        </InputLabel>
        <TextField
          id="title"
          type="text"
          variant="filled"
          placeholder="Title"
          autoComplete="title"
          fullWidth
          required
          {...formik.getFieldProps('title')}
        />
      </Box>

      <Box>
        <InputLabel
          component="label"
          sx={{ fontSize: '12px', marginBottom: '20px' }}
          size="small"
          htmlFor="content"
        >
            Content
        </InputLabel>
        <TextField
          id="content"
          type="text"
          variant="filled"
          placeholder="Content"
          autoComplete="content"
          fullWidth
          required
          {...formik.getFieldProps('content')}
        />
      </Box>

      <Box>
        <InputLabel
          component="label"
          sx={{ fontSize: '12px', marginBottom: '20px' }}
          size="small"
          htmlFor="page"
        >
            Page
        </InputLabel>
        <TextField
          id="page"
          type="text"
          variant="filled"
          placeholder="Page type"
          autoComplete="page"
          fullWidth
          required
          disabled={true}
          {...formik.getFieldProps('page')}
        />
      </Box>

      <Box>
        <InputLabel
          component="label"
          sx={{ fontSize: '12px', marginBottom: '20px' }}
          size="small"
          htmlFor="order"
        >
          Order
        </InputLabel>
        <TextField
          id="order"
          type="number"
          variant="filled"
          placeholder="Order"
          autoComplete="order"
          required
          fullWidth
          {...formik.getFieldProps('order')}
        />
      </Box>

      <Button
        type="submit"
        variant="contained"
        size="medium"
        disabled={formik.isSubmitting}
        fullWidth
        sx={{ marginTop: '20px' }}
      >
        {formik.isSubmitting ? 'Processing...' : 'Add Faq'}
      </Button>
    </form>
  );
}