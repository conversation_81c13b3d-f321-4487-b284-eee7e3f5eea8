import { getAllRequests } from "pages/templates/api/getAllRequests.ts";
import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
export default function useAllRequests() {
    const { data, loading, refetch } = useQueryFix({
        query: useQuery({
            queryKey: 'all-requests',
            queryFn: () => getAllRequests(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.requests,
    });
    return { data, loading, refetch };
}
