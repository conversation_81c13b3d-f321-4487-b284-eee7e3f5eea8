import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery, useQueryClient } from 'react-query';
import { deleteGiveAway } from 'pages/give-away/api/deleteGiveAway.ts';

export default function useDeleteGiveAway(props: { _id: string }) {
  const queryClient = useQueryClient();
  const { refetch: giveAwayDelete, loading: giveAwayDelLoading } = useQueryFix({
    query: useQuery({
      queryKey: "",
      queryFn: () => deleteGiveAway(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['giveAway']).finally(),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { giveAwayDelete, giveAwayDelLoading };
}