export interface CategoryDto {
    categoryName: Translations;
    _id: string;
    description: string;
    slug: string;
    metaTitle: string;
    metaDescription: string;
    metaKeywords: string;
    category_order: number;
    visibility: boolean;
    showInMainMenu: boolean;
    showImageOnMainMenu: boolean;
    isActive: boolean;
    image: string;
    parent_id: string | null;
    createdAt: string;
    updatedAt: string;
    __v: number;
    children: SubCategory[];
}

interface Translations {
    en: string;
    fr: string;
    it: string;
    de: string;
    es: string;
}

interface SubCategory {
    categoryName: Translations;
    _id: string;
    description: string;
    slug: string;
    metaTitle: string;
    metaDescription: string;
    metaKeywords: string;
    category_order: number;
    visibility: boolean;
    showInMainMenu: boolean;
    showImageOnMainMenu: boolean;
    isActive: boolean;
    image: string;
    parent_id: string | null;
    createdAt: string;
    updatedAt: string;
}