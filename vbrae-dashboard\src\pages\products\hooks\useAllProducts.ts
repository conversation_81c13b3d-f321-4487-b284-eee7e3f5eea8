import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getAllProducts } from 'pages/products/api/getAllProducts.ts';

export default function useAllProducts({query}: { query: string }) {
  const { data: products, loading: productLoading, refetch: productRefetch } = useQueryFix({
    query: useQuery({
      queryKey: ['all-products', query],
      queryFn: () => getAllProducts({query}),
      onError: showError,
    }),
    transform: (data) => data.data,
  });
  return { products, productLoading, productRefetch };
}
