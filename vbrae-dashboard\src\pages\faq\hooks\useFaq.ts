import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getAllFaq } from 'pages/faq/api/getAllFaq.ts';

export default function useFaq() {
    const { data: faq, loading: faqLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['faqs'],
            queryFn: () => getAllFaq(),
            onError: showError,
        }),
        transform: (data) => data.data,
    });
    return { faq, faqLoading };
}
