import { getRequest } from 'vbrae-utils';
import { BlogPost } from 'pages/blogs/types/blog.ts';
import { BlogPropsDto } from 'pages/blogs/hooks/useBlogs.ts';

type APIResponse = {
  data: BlogPost[];
};

export async function getBlogs({language}:BlogPropsDto): Promise<APIResponse> {
    return await getRequest({
        url: `blog${language ? `?language=${language}` : ''}`,
        useAuth: true,
    });
}
