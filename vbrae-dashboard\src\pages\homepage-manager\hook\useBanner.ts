import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getAllBanner } from 'pages/homepage-manager/api/getAllBanner.ts';

export default function useBanner() {
  const { data: banner, loading: bannerLoading } = useQueryFix({
    query: useQuery({
      queryKey: ['all-banners'],
      queryFn: () => getAllBanner(),
      onError: showError,
    }),
    transform: (data) => data.data.banners,
  });
  return { banner, bannerLoading };
}
