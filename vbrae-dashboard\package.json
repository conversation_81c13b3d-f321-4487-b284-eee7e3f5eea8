{"name": "dashdark-x", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "predeploy": "vite build && cp ./dist/index.html ./dist/404.html", "deploy": "gh-pages -d dist"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.18", "@mui/material": "^5.15.17", "@mui/x-data-grid": "^7.6.2", "@mui/x-date-pickers": "^7.4.0", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.20.6", "@tinymce/tinymce-react": "^5.1.1", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "formik": "^2.4.6", "pusher-js": "^8.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-query": "^3.39.3", "react-router-dom": "^6.22.3", "sweetalert2": "^11.15.3", "vite-plugin-checker": "^0.6.4", "vite-tsconfig-paths": "^4.3.2", "yup": "^1.6.1"}, "devDependencies": {"@iconify/react": "^4.1.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "prettier": "^3.2.5", "typescript": "^5.2.2", "vite": "^5.2.0"}}