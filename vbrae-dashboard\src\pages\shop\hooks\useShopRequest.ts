import { showError, useQueryFix } from 'vbrae-utils';
import { useQuery } from 'react-query';
import { getAllShops } from 'pages/shop/api/getAllShops.ts';

export default function useShopRequest() {
  const {
    data: shopRequests,
    loading: shopLoading,
    refetch: shopRefetch,
  } = useQueryFix({
    query: useQuery({
      queryKey: ['all-shop-requests'],
      queryFn: () => getAllShops(),
      onError: showError,
    }),
    transform: (data) => data.data,
  });
  return { shopRequests, shopLoading, shopRefetch };
}
