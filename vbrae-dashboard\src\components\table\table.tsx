import { Dispatch, SetStateAction, useState } from 'react';
import './table.css';
import {
  ColumnDef,
  ColumnResizeDirection,
  ColumnResizeMode,
  FilterFn,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { rankItem } from '@tanstack/match-sorter-utils';
import Pagination from '@mui/material/Pagination';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography } from '@mui/material';

type DataTableProps<T> = {
  globalFilter?: string;
  setGlobalFilter?: Dispatch<SetStateAction<string>>;
  columns: ColumnDef<T>[];
  data: T[];
  pageSize?: number;
  tableTitle?: string;
};

const fuzzyFilter: FilterFn<unknown> = (row, columnId, value, addMeta) => {
  const rawValue = row.getValue(columnId);
  const stringValue =
      Array.isArray(rawValue) ? rawValue.join(' ') : String(rawValue || '');

  const itemRank = rankItem(stringValue, value);
  addMeta({
    itemRank,
  });

  return itemRank.passed;
};

export function TableComponent<T>({
                                    globalFilter,
                                    setGlobalFilter,
                                    data,
                                    columns,
                                    tableTitle,
                                  }: DataTableProps<T>) {
  const [columnResizeMode] = useState<ColumnResizeMode>('onChange');
  const [columnResizeDirection] = useState<ColumnResizeDirection>('ltr');
  const table = useReactTable({
    data,
    columns: columns as ColumnDef<unknown, any>[],
    debugTable: true,
    filterFns: {
      fuzzy: fuzzyFilter,
    },
    columnResizeMode,
    columnResizeDirection,
    state: {
      globalFilter
    },
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    globalFilterFn: fuzzyFilter,
  });


  return (
      <div>
        <TableContainer component={Paper} sx={{ overflowX: 'auto', borderRadius: 2, p: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>
            {tableTitle}
          </Typography>
          <Table sx={{ minWidth: 650 }}>
            <TableHead>
              {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                        <TableCell
                            key={header.id}
                            sx={{
                              textAlign: 'left',
                              fontSize: '1rem',
                              pr: 2.5,
                              width: header.getSize(),
                              fontWeight: 500,
                            }}
                        >
                          {header.isPlaceholder ? null : (
                              <div>{flexRender(header.column.columnDef.header, header.getContext())}</div>
                          )}
                        </TableCell>
                    ))}
                  </TableRow>
              ))}
            </TableHead>
            <TableBody>
              {table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                        <TableCell
                            key={cell.id}
                            sx={{
                              p: 2,
                              pr: 2,
                              fontSize: '1rem',
                            }}
                        >
                          <div>{flexRender(cell.column.columnDef.cell, cell.getContext())}</div>
                        </TableCell>
                    ))}
                  </TableRow>
              ))}
            </TableBody>
          </Table>
          {!table.getRowModel().rows.length && (
              <Typography variant="h6" sx={{ fontWeight: 'bold', mt: 4, textAlign: 'center' }}>
                Nothing here yet!
              </Typography>
          )}
        </TableContainer>

        {table.getPageCount() > 1 && (
            <div style={{ display: 'flex', justifyContent: 'center', marginTop: 16 }}>
              <Pagination
                  count={table.getPageCount()}
                  page={table.getState().pagination.pageIndex + 1}
                  onChange={(_, page) => table.setPageIndex(page - 1)}
                  siblingCount={1}
                  boundaryCount={1}
                  sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}
              />
            </div>
        )}
      </div>
  );
}
