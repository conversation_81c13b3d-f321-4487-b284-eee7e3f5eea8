import Stack from '@mui/material/Stack';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import {useEffect, useMemo, useState} from 'react';
import { ColumnDef } from '@tanstack/react-table';
import IconifyIcon from 'components/base/IconifyIcon.tsx';
import {AvatarGroup, InputBase} from '@mui/material';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
import Box from '@mui/material/Box';
import { TableComponent } from 'components/table/table.tsx';
import { BannerDto, KeyValues } from 'pages/homepage-manager/types/homepage-manager.ts';
import BannerModal from 'components/modals/BannerModal.tsx';
import SettingForm from 'pages/homepage-manager/components/SettingForm.tsx';
import useBanner from 'pages/homepage-manager/hook/useBanner.ts';
import CircularProgress from '@mui/material/CircularProgress';
import Avatar from '@mui/material/Avatar';
import useDeleteBanner from "pages/homepage-manager/hook/useDeleteBanner.ts";

export default function HomepageManager() {
  const [open, setOpen] = useState<KeyValues>({
    _id: undefined,
    open: false,
  });
  const [itemToDelete, setItemToDelete] = useState<string>('');

  const { banner, bannerLoading } = useBanner();
  const {deleteRefetch} = useDeleteBanner({_id: itemToDelete});

  const [searchValue, setSearchValue] = useState('');
  const columns = useMemo<ColumnDef<BannerDto>[]>(
    () => [
      {
        header: 'Sr. No',
        accessorFn: (_, index) => `${index + 1}`,
        id: '_index',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Banner',
        accessorFn: (row) => row.images,
        id: 'image',
        cell: (info) => <AvatarGroup spacing={24} sx={{justifyContent: "flex-end"}}>
            <Avatar alt="Remy Sharp" src={info.row.original.images.desktop} />
        </AvatarGroup>,
        enableGlobalFilter: true,
      },
      {
        header: 'URL',
        accessorFn: (row) => row.link,
        id: 'link',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Order',
        accessorFn: (row) => row.order,
        id: 'order',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Location',
        accessorFn: (row) => row.category,
        id: 'location',
        cell: (info) => info.getValue(),
        enableGlobalFilter: true,
      },
      {
        header: 'Action',
        accessorFn: () => {},
        id: 'action',
        cell: (info) => (
          <Stack direction="row" spacing={1} alignItems="center">
            <Button onClick={() => setOpen({_id: info.row.original._id, open: true})} sx={{ padding: 0, minWidth: 0 }}>
              <IconifyIcon icon="ion:create-outline" sx={{ fontSize: '20px' }} />
            </Button>
            <Button sx={{ padding: 0, minWidth: 0 }} onClick={()=> setItemToDelete(info.row.original._id)}>
              <IconifyIcon icon="ion:trash-outline" sx={{ fontSize: '20px' }} />
            </Button>
          </Stack>
        ),
        enableGlobalFilter: false, // Actions don't need to be globally searchable
      },
    ],
    [],
  );

    useEffect(() => {
        if(itemToDelete) deleteRefetch().finally(()=> setItemToDelete(""))
    }, [itemToDelete]);

  if (bannerLoading || !banner) {
    return <CircularProgress />;
  }

  return (
    <>
      <BannerModal open={open} setOpen={setOpen} />
      <Stack spacing={2} direction="column">
        <Paper elevation={3} sx={{ py: 4, width: '100%' }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between" gap={2}>
            <Typography variant="h5" fontWeight={600}>
              Homepage Banners
            </Typography>
            <Button
              type="submit"
              variant="contained"
              size="small"
              onClick={() => setOpen({ _id: undefined, open: true })}
            >
              Add Banner
            </Button>
          </Stack>
        </Paper>
        <Paper
          component="form"
          sx={{
            p: '2px 4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: { xs: '100%', sm: 400 },
            marginLeft: 'auto',
          }}
        >
          <InputBase
            sx={{ ml: 1, flex: 1, border: 'none' }}
            placeholder="Search here"
            inputProps={{ 'aria-label': 'search google maps' }}
            onChange={(e) => setSearchValue(e.target.value.trim())}
          />
          <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
            <SearchIcon />
          </IconButton>
        </Paper>

        <Box sx={{ overflowX: 'auto', width: '100%' }}>
          <TableComponent
            columns={columns}
            data={banner}
            globalFilter={searchValue}
            setGlobalFilter={setSearchValue}
          />
        </Box>
      </Stack>

      <Stack spacing={2} direction="column">
        <Paper elevation={3} sx={{ py: 4, width: '50%' }}>
          <Typography variant="h6" fontWeight={400}>
            Settings
          </Typography>
          <SettingForm />
        </Paper>
      </Stack>
    </>
  );
};